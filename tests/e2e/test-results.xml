<testsuites id="" name="" tests="75" failures="70" skipped="0" errors="0" time="139.095738">
<testsuite name="channel_management.spec.ts" timestamp="2025-08-03T18:39:01.375Z" hostname="chromium" tests="15" failures="14" skipped="0" time="131.498" errors="0">
<testcase name="渠道管理E2E测试 › 渠道连接流程 › 应该成功连接新渠道" classname="channel_management.spec.ts" time="7.517">
<failure message="channel_management.spec.ts:172:9 应该成功连接新渠道" type="FAILURE">
<![CDATA[  [chromium] › channel_management.spec.ts:172:9 › 渠道管理E2E测试 › 渠道连接流程 › 应该成功连接新渠道 ───────────────────

    Error: Timed out 5000ms waiting for expect(locator).toContainText(expected)

    Locator: locator('h1')
    Expected string: "渠道管理"
    Received: <element(s) not found>
    Call log:
      - Expect "toContainText" with timeout 5000ms
      - waiting for locator('h1')


      174 |
      175 |       // 验证页面加载
    > 176 |       await expect(page.locator('h1')).toContainText('渠道管理')
          |                                        ^
      177 |
      178 |       // 点击添加渠道
      179 |       await channelPage.clickAddChannel()
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:176:40

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该成功连接新渠道-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该成功连接新渠道-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该成功连接新渠道-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该成功连接新渠道-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 渠道连接流程 › 应该处理无效Cookie" classname="channel_management.spec.ts" time="12.476">
<failure message="channel_management.spec.ts:205:9 应该处理无效Cookie" type="FAILURE">
<![CDATA[  [chromium] › channel_management.spec.ts:205:9 › 渠道管理E2E测试 › 渠道连接流程 › 应该处理无效Cookie ────────────────

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="add-channel-button"]')


      31 |   // 添加渠道
      32 |   async clickAddChannel() {
    > 33 |     await this.page.click('[data-testid="add-channel-button"]')
         |                     ^
      34 |   }
      35 |
      36 |   // 选择平台
        at ChannelManagementPage.clickAddChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:33:21)
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:208:25

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理无效Cookie-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理无效Cookie-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理无效Cookie-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理无效Cookie-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 渠道连接流程 › 应该处理重复渠道连接" classname="channel_management.spec.ts" time="12.612">
<failure message="channel_management.spec.ts:222:9 应该处理重复渠道连接" type="FAILURE">
<![CDATA[  [chromium] › channel_management.spec.ts:222:9 › 渠道管理E2E测试 › 渠道连接流程 › 应该处理重复渠道连接 ──────────────────

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="add-channel-button"]')


      31 |   // 添加渠道
      32 |   async clickAddChannel() {
    > 33 |     await this.page.click('[data-testid="add-channel-button"]')
         |                     ^
      34 |   }
      35 |
      36 |   // 选择平台
        at ChannelManagementPage.clickAddChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:33:21)
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:236:25

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理重复渠道连接-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理重复渠道连接-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理重复渠道连接-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理重复渠道连接-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 渠道管理操作 › 应该显示渠道列表" classname="channel_management.spec.ts" time="7.482">
<failure message="channel_management.spec.ts:287:9 应该显示渠道列表" type="FAILURE">
<![CDATA[  [chromium] › channel_management.spec.ts:287:9 › 渠道管理E2E测试 › 渠道管理操作 › 应该显示渠道列表 ────────────────────

    Error: Timed out 5000ms waiting for expect(locator).toHaveCount(expected)

    Locator: locator('[data-testid="channel-list"] [data-testid^="channel-"]')
    Expected: 2
    Received: 0
    Call log:
      - Expect "toHaveCount" with timeout 5000ms
      - waiting for locator('[data-testid="channel-list"] [data-testid^="channel-"]')
        9 × locator resolved to 0 elements
          - unexpected value "0"


      290 |       // 验证渠道列表显示
      291 |       const channels = await channelPage.getChannelList()
    > 292 |       await expect(channels).toHaveCount(2)
          |                              ^
      293 |
      294 |       // 验证渠道信息
      295 |       await expect(page.locator('[data-testid="channel-channel-1"]')).toContainText('主店铺')
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:292:30

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该显示渠道列表-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该显示渠道列表-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该显示渠道列表-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该显示渠道列表-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 渠道管理操作 › 应该支持搜索功能" classname="channel_management.spec.ts" time="12.573">
<failure message="channel_management.spec.ts:303:9 应该支持搜索功能" type="FAILURE">
<![CDATA[  [chromium] › channel_management.spec.ts:303:9 › 渠道管理E2E测试 › 渠道管理操作 › 应该支持搜索功能 ────────────────────

    TimeoutError: page.fill: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="search-input"]')


      61 |   // 搜索渠道
      62 |   async searchChannel(query: string) {
    > 63 |     await this.page.fill('[data-testid="search-input"]', query)
         |                     ^
      64 |     await this.page.keyboard.press('Enter')
      65 |   }
      66 |
        at ChannelManagementPage.searchChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:63:21)
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:307:25

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持搜索功能-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持搜索功能-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持搜索功能-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持搜索功能-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 渠道管理操作 › 应该支持状态筛选" classname="channel_management.spec.ts" time="11.304">
<failure message="channel_management.spec.ts:314:9 应该支持状态筛选" type="FAILURE">
<![CDATA[  [chromium] › channel_management.spec.ts:314:9 › 渠道管理E2E测试 › 渠道管理操作 › 应该支持状态筛选 ────────────────────

    TimeoutError: page.selectOption: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="status-filter"]')


      67 |   // 筛选渠道
      68 |   async filterByStatus(status: string) {
    > 69 |     await this.page.selectOption('[data-testid="status-filter"]', status)
         |                     ^
      70 |   }
      71 |
      72 |   // 删除渠道
        at ChannelManagementPage.filterByStatus (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:69:21)
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:318:25

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持状态筛选-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持状态筛选-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持状态筛选-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持状态筛选-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 渠道管理操作 › 应该支持删除和恢复渠道" classname="channel_management.spec.ts" time="11.49">
<failure message="channel_management.spec.ts:325:9 应该支持删除和恢复渠道" type="FAILURE">
<![CDATA[  [chromium] › channel_management.spec.ts:325:9 › 渠道管理E2E测试 › 渠道管理操作 › 应该支持删除和恢复渠道 ─────────────────

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="channel-channel-1"] [data-testid="delete-button"]')


      72 |   // 删除渠道
      73 |   async deleteChannel(channelId: string) {
    > 74 |     await this.page.click(`[data-testid="channel-${channelId}"] [data-testid="delete-button"]`)
         |                     ^
      75 |     await this.page.click('[data-testid="confirm-delete"]')
      76 |   }
      77 |
        at ChannelManagementPage.deleteChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:74:21)
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:348:25

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持删除和恢复渠道-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持删除和恢复渠道-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持删除和恢复渠道-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持删除和恢复渠道-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 状态监控功能 › 应该显示实时状态监控" classname="channel_management.spec.ts" time="11.321">
<failure message="channel_management.spec.ts:365:9 应该显示实时状态监控" type="FAILURE">
<![CDATA[  [chromium] › channel_management.spec.ts:365:9 › 渠道管理E2E测试 › 状态监控功能 › 应该显示实时状态监控 ──────────────────

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="channel-channel-1"] [data-testid="monitor-button"]')


      84 |   // 打开监控面板
      85 |   async openMonitorPanel(channelId: string) {
    > 86 |     await this.page.click(`[data-testid="channel-${channelId}"] [data-testid="monitor-button"]`)
         |                     ^
      87 |   }
      88 |
      89 |   // 重连渠道
        at ChannelManagementPage.openMonitorPanel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:86:21)
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:401:25

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-状态监控功能-应该显示实时状态监控-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-状态监控功能-应该显示实时状态监控-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-状态监控功能-应该显示实时状态监控-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-状态监控功能-应该显示实时状态监控-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 状态监控功能 › 应该支持手动重连" classname="channel_management.spec.ts" time="11.332">
<failure message="channel_management.spec.ts:411:9 应该支持手动重连" type="FAILURE">
<![CDATA[  [chromium] › channel_management.spec.ts:411:9 › 渠道管理E2E测试 › 状态监控功能 › 应该支持手动重连 ────────────────────

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="channel-channel-2"] [data-testid="monitor-button"]')


      84 |   // 打开监控面板
      85 |   async openMonitorPanel(channelId: string) {
    > 86 |     await this.page.click(`[data-testid="channel-${channelId}"] [data-testid="monitor-button"]`)
         |                     ^
      87 |   }
      88 |
      89 |   // 重连渠道
        at ChannelManagementPage.openMonitorPanel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:86:21)
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:418:25

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-状态监控功能-应该支持手动重连-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-状态监控功能-应该支持手动重连-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-状态监控功能-应该支持手动重连-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-状态监控功能-应该支持手动重连-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 错误处理场景 › 应该处理网络错误" classname="channel_management.spec.ts" time="6.327">
<failure message="channel_management.spec.ts:433:9 应该处理网络错误" type="FAILURE">
<![CDATA[  [chromium] › channel_management.spec.ts:433:9 › 渠道管理E2E测试 › 错误处理场景 › 应该处理网络错误 ────────────────────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('[data-testid="network-error"]')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('[data-testid="network-error"]')


      440 |
      441 |       // 验证错误提示
    > 442 |       await expect(page.locator('[data-testid="network-error"]')).toBeVisible()
          |                                                                   ^
      443 |       await expect(page.locator('[data-testid="retry-button"]')).toBeVisible()
      444 |
      445 |       // 点击重试
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:442:67

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理网络错误-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理网络错误-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理网络错误-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理网络错误-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 错误处理场景 › 应该处理服务器错误" classname="channel_management.spec.ts" time="6.524">
<failure message="channel_management.spec.ts:449:9 应该处理服务器错误" type="FAILURE">
<![CDATA[  [chromium] › channel_management.spec.ts:449:9 › 渠道管理E2E测试 › 错误处理场景 › 应该处理服务器错误 ───────────────────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('[data-testid="server-error"]')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('[data-testid="server-error"]')


      462 |
      463 |       // 验证错误提示
    > 464 |       await expect(page.locator('[data-testid="server-error"]')).toBeVisible()
          |                                                                  ^
      465 |       await expect(page.locator('[data-testid="error-message"]')).toContainText('服务器内部错误')
      466 |     })
      467 |   })
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:464:66

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理服务器错误-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理服务器错误-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理服务器错误-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理服务器错误-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 响应式设计测试 › 应该在移动端正确显示" classname="channel_management.spec.ts" time="6.499">
<failure message="channel_management.spec.ts:470:9 应该在移动端正确显示" type="FAILURE">
<![CDATA[  [chromium] › channel_management.spec.ts:470:9 › 渠道管理E2E测试 › 响应式设计测试 › 应该在移动端正确显示 ─────────────────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('[data-testid="mobile-layout"]')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('[data-testid="mobile-layout"]')


      475 |
      476 |       // 验证移动端布局
    > 477 |       await expect(page.locator('[data-testid="mobile-layout"]')).toBeVisible()
          |                                                                   ^
      478 |       await expect(page.locator('[data-testid="mobile-menu"]')).toBeVisible()
      479 |
      480 |       // 验证响应式表格
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:477:67

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在移动端正确显示-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在移动端正确显示-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在移动端正确显示-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在移动端正确显示-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 响应式设计测试 › 应该在平板端正确显示" classname="channel_management.spec.ts" time="6.38">
<failure message="channel_management.spec.ts:484:9 应该在平板端正确显示" type="FAILURE">
<![CDATA[  [chromium] › channel_management.spec.ts:484:9 › 渠道管理E2E测试 › 响应式设计测试 › 应该在平板端正确显示 ─────────────────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('[data-testid="tablet-layout"]')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('[data-testid="tablet-layout"]')


      489 |
      490 |       // 验证平板端布局
    > 491 |       await expect(page.locator('[data-testid="tablet-layout"]')).toBeVisible()
          |                                                                   ^
      492 |     })
      493 |   })
      494 |
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:491:67

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在平板端正确显示-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在平板端正确显示-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在平板端正确显示-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在平板端正确显示-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 性能测试 › 页面加载性能应该符合要求" classname="channel_management.spec.ts" time="1.309">
</testcase>
<testcase name="渠道管理E2E测试 › 性能测试 › 大量数据渲染性能" classname="channel_management.spec.ts" time="6.352">
<failure message="channel_management.spec.ts:510:9 大量数据渲染性能" type="FAILURE">
<![CDATA[  [chromium] › channel_management.spec.ts:510:9 › 渠道管理E2E测试 › 性能测试 › 大量数据渲染性能 ──────────────────────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('[data-testid="channel-list"]')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('[data-testid="channel-list"]')


      538 |       
      539 |       // 等待列表渲染完成
    > 540 |       await expect(page.locator('[data-testid="channel-list"]')).toBeVisible()
          |                                                                  ^
      541 |       
      542 |       const renderTime = Date.now() - startTime
      543 |       
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:540:66

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-性能测试-大量数据渲染性能-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-性能测试-大量数据渲染性能-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-性能测试-大量数据渲染性能-chromium/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-性能测试-大量数据渲染性能-chromium/error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="channel_management.spec.ts" timestamp="2025-08-03T18:39:01.375Z" hostname="firefox" tests="15" failures="14" skipped="0" time="127.986" errors="0">
<testcase name="渠道管理E2E测试 › 渠道连接流程 › 应该成功连接新渠道" classname="channel_management.spec.ts" time="6.788">
<failure message="channel_management.spec.ts:172:9 应该成功连接新渠道" type="FAILURE">
<![CDATA[  [firefox] › channel_management.spec.ts:172:9 › 渠道管理E2E测试 › 渠道连接流程 › 应该成功连接新渠道 ────────────────────

    Error: Timed out 5000ms waiting for expect(locator).toContainText(expected)

    Locator: locator('h1')
    Expected string: "渠道管理"
    Received: <element(s) not found>
    Call log:
      - Expect "toContainText" with timeout 5000ms
      - waiting for locator('h1')


      174 |
      175 |       // 验证页面加载
    > 176 |       await expect(page.locator('h1')).toContainText('渠道管理')
          |                                        ^
      177 |
      178 |       // 点击添加渠道
      179 |       await channelPage.clickAddChannel()
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:176:40

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该成功连接新渠道-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该成功连接新渠道-firefox/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该成功连接新渠道-firefox/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该成功连接新渠道-firefox/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 渠道连接流程 › 应该处理无效Cookie" classname="channel_management.spec.ts" time="11.828">
<failure message="channel_management.spec.ts:205:9 应该处理无效Cookie" type="FAILURE">
<![CDATA[  [firefox] › channel_management.spec.ts:205:9 › 渠道管理E2E测试 › 渠道连接流程 › 应该处理无效Cookie ─────────────────

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="add-channel-button"]')


      31 |   // 添加渠道
      32 |   async clickAddChannel() {
    > 33 |     await this.page.click('[data-testid="add-channel-button"]')
         |                     ^
      34 |   }
      35 |
      36 |   // 选择平台
        at ChannelManagementPage.clickAddChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:33:21)
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:208:25

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理无效Cookie-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理无效Cookie-firefox/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理无效Cookie-firefox/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理无效Cookie-firefox/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 渠道连接流程 › 应该处理重复渠道连接" classname="channel_management.spec.ts" time="11.694">
<failure message="channel_management.spec.ts:222:9 应该处理重复渠道连接" type="FAILURE">
<![CDATA[  [firefox] › channel_management.spec.ts:222:9 › 渠道管理E2E测试 › 渠道连接流程 › 应该处理重复渠道连接 ───────────────────

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="add-channel-button"]')


      31 |   // 添加渠道
      32 |   async clickAddChannel() {
    > 33 |     await this.page.click('[data-testid="add-channel-button"]')
         |                     ^
      34 |   }
      35 |
      36 |   // 选择平台
        at ChannelManagementPage.clickAddChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:33:21)
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:236:25

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理重复渠道连接-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理重复渠道连接-firefox/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理重复渠道连接-firefox/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理重复渠道连接-firefox/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 渠道管理操作 › 应该显示渠道列表" classname="channel_management.spec.ts" time="6.892">
<failure message="channel_management.spec.ts:287:9 应该显示渠道列表" type="FAILURE">
<![CDATA[  [firefox] › channel_management.spec.ts:287:9 › 渠道管理E2E测试 › 渠道管理操作 › 应该显示渠道列表 ─────────────────────

    Error: Timed out 5000ms waiting for expect(locator).toHaveCount(expected)

    Locator: locator('[data-testid="channel-list"] [data-testid^="channel-"]')
    Expected: 2
    Received: 0
    Call log:
      - Expect "toHaveCount" with timeout 5000ms
      - waiting for locator('[data-testid="channel-list"] [data-testid^="channel-"]')
        9 × locator resolved to 0 elements
          - unexpected value "0"


      290 |       // 验证渠道列表显示
      291 |       const channels = await channelPage.getChannelList()
    > 292 |       await expect(channels).toHaveCount(2)
          |                              ^
      293 |
      294 |       // 验证渠道信息
      295 |       await expect(page.locator('[data-testid="channel-channel-1"]')).toContainText('主店铺')
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:292:30

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该显示渠道列表-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该显示渠道列表-firefox/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该显示渠道列表-firefox/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该显示渠道列表-firefox/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 渠道管理操作 › 应该支持搜索功能" classname="channel_management.spec.ts" time="11.663">
<failure message="channel_management.spec.ts:303:9 应该支持搜索功能" type="FAILURE">
<![CDATA[  [firefox] › channel_management.spec.ts:303:9 › 渠道管理E2E测试 › 渠道管理操作 › 应该支持搜索功能 ─────────────────────

    TimeoutError: page.fill: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="search-input"]')


      61 |   // 搜索渠道
      62 |   async searchChannel(query: string) {
    > 63 |     await this.page.fill('[data-testid="search-input"]', query)
         |                     ^
      64 |     await this.page.keyboard.press('Enter')
      65 |   }
      66 |
        at ChannelManagementPage.searchChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:63:21)
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:307:25

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持搜索功能-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持搜索功能-firefox/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持搜索功能-firefox/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持搜索功能-firefox/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 渠道管理操作 › 应该支持状态筛选" classname="channel_management.spec.ts" time="11.448">
<failure message="channel_management.spec.ts:314:9 应该支持状态筛选" type="FAILURE">
<![CDATA[  [firefox] › channel_management.spec.ts:314:9 › 渠道管理E2E测试 › 渠道管理操作 › 应该支持状态筛选 ─────────────────────

    TimeoutError: page.selectOption: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="status-filter"]')


      67 |   // 筛选渠道
      68 |   async filterByStatus(status: string) {
    > 69 |     await this.page.selectOption('[data-testid="status-filter"]', status)
         |                     ^
      70 |   }
      71 |
      72 |   // 删除渠道
        at ChannelManagementPage.filterByStatus (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:69:21)
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:318:25

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持状态筛选-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持状态筛选-firefox/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持状态筛选-firefox/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持状态筛选-firefox/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 渠道管理操作 › 应该支持删除和恢复渠道" classname="channel_management.spec.ts" time="11.47">
<failure message="channel_management.spec.ts:325:9 应该支持删除和恢复渠道" type="FAILURE">
<![CDATA[  [firefox] › channel_management.spec.ts:325:9 › 渠道管理E2E测试 › 渠道管理操作 › 应该支持删除和恢复渠道 ──────────────────

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="channel-channel-1"] [data-testid="delete-button"]')


      72 |   // 删除渠道
      73 |   async deleteChannel(channelId: string) {
    > 74 |     await this.page.click(`[data-testid="channel-${channelId}"] [data-testid="delete-button"]`)
         |                     ^
      75 |     await this.page.click('[data-testid="confirm-delete"]')
      76 |   }
      77 |
        at ChannelManagementPage.deleteChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:74:21)
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:348:25

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持删除和恢复渠道-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持删除和恢复渠道-firefox/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持删除和恢复渠道-firefox/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持删除和恢复渠道-firefox/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 状态监控功能 › 应该显示实时状态监控" classname="channel_management.spec.ts" time="11.377">
<failure message="channel_management.spec.ts:365:9 应该显示实时状态监控" type="FAILURE">
<![CDATA[  [firefox] › channel_management.spec.ts:365:9 › 渠道管理E2E测试 › 状态监控功能 › 应该显示实时状态监控 ───────────────────

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="channel-channel-1"] [data-testid="monitor-button"]')


      84 |   // 打开监控面板
      85 |   async openMonitorPanel(channelId: string) {
    > 86 |     await this.page.click(`[data-testid="channel-${channelId}"] [data-testid="monitor-button"]`)
         |                     ^
      87 |   }
      88 |
      89 |   // 重连渠道
        at ChannelManagementPage.openMonitorPanel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:86:21)
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:401:25

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-状态监控功能-应该显示实时状态监控-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-状态监控功能-应该显示实时状态监控-firefox/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-状态监控功能-应该显示实时状态监控-firefox/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-状态监控功能-应该显示实时状态监控-firefox/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 状态监控功能 › 应该支持手动重连" classname="channel_management.spec.ts" time="11.439">
<failure message="channel_management.spec.ts:411:9 应该支持手动重连" type="FAILURE">
<![CDATA[  [firefox] › channel_management.spec.ts:411:9 › 渠道管理E2E测试 › 状态监控功能 › 应该支持手动重连 ─────────────────────

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="channel-channel-2"] [data-testid="monitor-button"]')


      84 |   // 打开监控面板
      85 |   async openMonitorPanel(channelId: string) {
    > 86 |     await this.page.click(`[data-testid="channel-${channelId}"] [data-testid="monitor-button"]`)
         |                     ^
      87 |   }
      88 |
      89 |   // 重连渠道
        at ChannelManagementPage.openMonitorPanel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:86:21)
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:418:25

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-状态监控功能-应该支持手动重连-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-状态监控功能-应该支持手动重连-firefox/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-状态监控功能-应该支持手动重连-firefox/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-状态监控功能-应该支持手动重连-firefox/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 错误处理场景 › 应该处理网络错误" classname="channel_management.spec.ts" time="6.365">
<failure message="channel_management.spec.ts:433:9 应该处理网络错误" type="FAILURE">
<![CDATA[  [firefox] › channel_management.spec.ts:433:9 › 渠道管理E2E测试 › 错误处理场景 › 应该处理网络错误 ─────────────────────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('[data-testid="network-error"]')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('[data-testid="network-error"]')


      440 |
      441 |       // 验证错误提示
    > 442 |       await expect(page.locator('[data-testid="network-error"]')).toBeVisible()
          |                                                                   ^
      443 |       await expect(page.locator('[data-testid="retry-button"]')).toBeVisible()
      444 |
      445 |       // 点击重试
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:442:67

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理网络错误-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理网络错误-firefox/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理网络错误-firefox/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理网络错误-firefox/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 错误处理场景 › 应该处理服务器错误" classname="channel_management.spec.ts" time="6.374">
<failure message="channel_management.spec.ts:449:9 应该处理服务器错误" type="FAILURE">
<![CDATA[  [firefox] › channel_management.spec.ts:449:9 › 渠道管理E2E测试 › 错误处理场景 › 应该处理服务器错误 ────────────────────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('[data-testid="server-error"]')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('[data-testid="server-error"]')


      462 |
      463 |       // 验证错误提示
    > 464 |       await expect(page.locator('[data-testid="server-error"]')).toBeVisible()
          |                                                                  ^
      465 |       await expect(page.locator('[data-testid="error-message"]')).toContainText('服务器内部错误')
      466 |     })
      467 |   })
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:464:66

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理服务器错误-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理服务器错误-firefox/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理服务器错误-firefox/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理服务器错误-firefox/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 响应式设计测试 › 应该在移动端正确显示" classname="channel_management.spec.ts" time="6.388">
<failure message="channel_management.spec.ts:470:9 应该在移动端正确显示" type="FAILURE">
<![CDATA[  [firefox] › channel_management.spec.ts:470:9 › 渠道管理E2E测试 › 响应式设计测试 › 应该在移动端正确显示 ──────────────────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('[data-testid="mobile-layout"]')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('[data-testid="mobile-layout"]')


      475 |
      476 |       // 验证移动端布局
    > 477 |       await expect(page.locator('[data-testid="mobile-layout"]')).toBeVisible()
          |                                                                   ^
      478 |       await expect(page.locator('[data-testid="mobile-menu"]')).toBeVisible()
      479 |
      480 |       // 验证响应式表格
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:477:67

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在移动端正确显示-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在移动端正确显示-firefox/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在移动端正确显示-firefox/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在移动端正确显示-firefox/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 响应式设计测试 › 应该在平板端正确显示" classname="channel_management.spec.ts" time="6.36">
<failure message="channel_management.spec.ts:484:9 应该在平板端正确显示" type="FAILURE">
<![CDATA[  [firefox] › channel_management.spec.ts:484:9 › 渠道管理E2E测试 › 响应式设计测试 › 应该在平板端正确显示 ──────────────────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('[data-testid="tablet-layout"]')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('[data-testid="tablet-layout"]')


      489 |
      490 |       // 验证平板端布局
    > 491 |       await expect(page.locator('[data-testid="tablet-layout"]')).toBeVisible()
          |                                                                   ^
      492 |     })
      493 |   })
      494 |
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:491:67

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在平板端正确显示-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在平板端正确显示-firefox/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在平板端正确显示-firefox/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在平板端正确显示-firefox/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 性能测试 › 页面加载性能应该符合要求" classname="channel_management.spec.ts" time="1.435">
</testcase>
<testcase name="渠道管理E2E测试 › 性能测试 › 大量数据渲染性能" classname="channel_management.spec.ts" time="6.465">
<failure message="channel_management.spec.ts:510:9 大量数据渲染性能" type="FAILURE">
<![CDATA[  [firefox] › channel_management.spec.ts:510:9 › 渠道管理E2E测试 › 性能测试 › 大量数据渲染性能 ───────────────────────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('[data-testid="channel-list"]')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('[data-testid="channel-list"]')


      538 |       
      539 |       // 等待列表渲染完成
    > 540 |       await expect(page.locator('[data-testid="channel-list"]')).toBeVisible()
          |                                                                  ^
      541 |       
      542 |       const renderTime = Date.now() - startTime
      543 |       
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:540:66

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-性能测试-大量数据渲染性能-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-性能测试-大量数据渲染性能-firefox/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-性能测试-大量数据渲染性能-firefox/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-性能测试-大量数据渲染性能-firefox/error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="channel_management.spec.ts" timestamp="2025-08-03T18:39:01.375Z" hostname="webkit" tests="15" failures="14" skipped="0" time="119.631" errors="0">
<testcase name="渠道管理E2E测试 › 渠道连接流程 › 应该成功连接新渠道" classname="channel_management.spec.ts" time="6.273">
<failure message="channel_management.spec.ts:172:9 应该成功连接新渠道" type="FAILURE">
<![CDATA[  [webkit] › channel_management.spec.ts:172:9 › 渠道管理E2E测试 › 渠道连接流程 › 应该成功连接新渠道 ─────────────────────

    Error: Timed out 5000ms waiting for expect(locator).toContainText(expected)

    Locator: locator('h1')
    Expected string: "渠道管理"
    Received: <element(s) not found>
    Call log:
      - Expect "toContainText" with timeout 5000ms
      - waiting for locator('h1')


      174 |
      175 |       // 验证页面加载
    > 176 |       await expect(page.locator('h1')).toContainText('渠道管理')
          |                                        ^
      177 |
      178 |       // 点击添加渠道
      179 |       await channelPage.clickAddChannel()
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:176:40

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该成功连接新渠道-webkit/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该成功连接新渠道-webkit/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该成功连接新渠道-webkit/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该成功连接新渠道-webkit/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 渠道连接流程 › 应该处理无效Cookie" classname="channel_management.spec.ts" time="11.049">
<failure message="channel_management.spec.ts:205:9 应该处理无效Cookie" type="FAILURE">
<![CDATA[  [webkit] › channel_management.spec.ts:205:9 › 渠道管理E2E测试 › 渠道连接流程 › 应该处理无效Cookie ──────────────────

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="add-channel-button"]')


      31 |   // 添加渠道
      32 |   async clickAddChannel() {
    > 33 |     await this.page.click('[data-testid="add-channel-button"]')
         |                     ^
      34 |   }
      35 |
      36 |   // 选择平台
        at ChannelManagementPage.clickAddChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:33:21)
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:208:25

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理无效Cookie-webkit/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理无效Cookie-webkit/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理无效Cookie-webkit/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理无效Cookie-webkit/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 渠道连接流程 › 应该处理重复渠道连接" classname="channel_management.spec.ts" time="11.087">
<failure message="channel_management.spec.ts:222:9 应该处理重复渠道连接" type="FAILURE">
<![CDATA[  [webkit] › channel_management.spec.ts:222:9 › 渠道管理E2E测试 › 渠道连接流程 › 应该处理重复渠道连接 ────────────────────

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="add-channel-button"]')


      31 |   // 添加渠道
      32 |   async clickAddChannel() {
    > 33 |     await this.page.click('[data-testid="add-channel-button"]')
         |                     ^
      34 |   }
      35 |
      36 |   // 选择平台
        at ChannelManagementPage.clickAddChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:33:21)
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:236:25

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理重复渠道连接-webkit/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理重复渠道连接-webkit/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理重复渠道连接-webkit/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理重复渠道连接-webkit/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 渠道管理操作 › 应该显示渠道列表" classname="channel_management.spec.ts" time="5.919">
<failure message="channel_management.spec.ts:287:9 应该显示渠道列表" type="FAILURE">
<![CDATA[  [webkit] › channel_management.spec.ts:287:9 › 渠道管理E2E测试 › 渠道管理操作 › 应该显示渠道列表 ──────────────────────

    Error: Timed out 5000ms waiting for expect(locator).toHaveCount(expected)

    Locator: locator('[data-testid="channel-list"] [data-testid^="channel-"]')
    Expected: 2
    Received: 0
    Call log:
      - Expect "toHaveCount" with timeout 5000ms
      - waiting for locator('[data-testid="channel-list"] [data-testid^="channel-"]')
        9 × locator resolved to 0 elements
          - unexpected value "0"


      290 |       // 验证渠道列表显示
      291 |       const channels = await channelPage.getChannelList()
    > 292 |       await expect(channels).toHaveCount(2)
          |                              ^
      293 |
      294 |       // 验证渠道信息
      295 |       await expect(page.locator('[data-testid="channel-channel-1"]')).toContainText('主店铺')
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:292:30

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该显示渠道列表-webkit/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该显示渠道列表-webkit/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该显示渠道列表-webkit/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该显示渠道列表-webkit/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 渠道管理操作 › 应该支持搜索功能" classname="channel_management.spec.ts" time="10.949">
<failure message="channel_management.spec.ts:303:9 应该支持搜索功能" type="FAILURE">
<![CDATA[  [webkit] › channel_management.spec.ts:303:9 › 渠道管理E2E测试 › 渠道管理操作 › 应该支持搜索功能 ──────────────────────

    TimeoutError: page.fill: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="search-input"]')


      61 |   // 搜索渠道
      62 |   async searchChannel(query: string) {
    > 63 |     await this.page.fill('[data-testid="search-input"]', query)
         |                     ^
      64 |     await this.page.keyboard.press('Enter')
      65 |   }
      66 |
        at ChannelManagementPage.searchChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:63:21)
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:307:25

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持搜索功能-webkit/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持搜索功能-webkit/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持搜索功能-webkit/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持搜索功能-webkit/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 渠道管理操作 › 应该支持状态筛选" classname="channel_management.spec.ts" time="10.902">
<failure message="channel_management.spec.ts:314:9 应该支持状态筛选" type="FAILURE">
<![CDATA[  [webkit] › channel_management.spec.ts:314:9 › 渠道管理E2E测试 › 渠道管理操作 › 应该支持状态筛选 ──────────────────────

    TimeoutError: page.selectOption: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="status-filter"]')


      67 |   // 筛选渠道
      68 |   async filterByStatus(status: string) {
    > 69 |     await this.page.selectOption('[data-testid="status-filter"]', status)
         |                     ^
      70 |   }
      71 |
      72 |   // 删除渠道
        at ChannelManagementPage.filterByStatus (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:69:21)
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:318:25

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持状态筛选-webkit/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持状态筛选-webkit/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持状态筛选-webkit/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持状态筛选-webkit/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 渠道管理操作 › 应该支持删除和恢复渠道" classname="channel_management.spec.ts" time="10.912">
<failure message="channel_management.spec.ts:325:9 应该支持删除和恢复渠道" type="FAILURE">
<![CDATA[  [webkit] › channel_management.spec.ts:325:9 › 渠道管理E2E测试 › 渠道管理操作 › 应该支持删除和恢复渠道 ───────────────────

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="channel-channel-1"] [data-testid="delete-button"]')


      72 |   // 删除渠道
      73 |   async deleteChannel(channelId: string) {
    > 74 |     await this.page.click(`[data-testid="channel-${channelId}"] [data-testid="delete-button"]`)
         |                     ^
      75 |     await this.page.click('[data-testid="confirm-delete"]')
      76 |   }
      77 |
        at ChannelManagementPage.deleteChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:74:21)
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:348:25

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持删除和恢复渠道-webkit/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持删除和恢复渠道-webkit/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持删除和恢复渠道-webkit/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持删除和恢复渠道-webkit/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 状态监控功能 › 应该显示实时状态监控" classname="channel_management.spec.ts" time="10.951">
<failure message="channel_management.spec.ts:365:9 应该显示实时状态监控" type="FAILURE">
<![CDATA[  [webkit] › channel_management.spec.ts:365:9 › 渠道管理E2E测试 › 状态监控功能 › 应该显示实时状态监控 ────────────────────

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="channel-channel-1"] [data-testid="monitor-button"]')


      84 |   // 打开监控面板
      85 |   async openMonitorPanel(channelId: string) {
    > 86 |     await this.page.click(`[data-testid="channel-${channelId}"] [data-testid="monitor-button"]`)
         |                     ^
      87 |   }
      88 |
      89 |   // 重连渠道
        at ChannelManagementPage.openMonitorPanel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:86:21)
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:401:25

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-状态监控功能-应该显示实时状态监控-webkit/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-状态监控功能-应该显示实时状态监控-webkit/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-状态监控功能-应该显示实时状态监控-webkit/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-状态监控功能-应该显示实时状态监控-webkit/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 状态监控功能 › 应该支持手动重连" classname="channel_management.spec.ts" time="10.924">
<failure message="channel_management.spec.ts:411:9 应该支持手动重连" type="FAILURE">
<![CDATA[  [webkit] › channel_management.spec.ts:411:9 › 渠道管理E2E测试 › 状态监控功能 › 应该支持手动重连 ──────────────────────

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="channel-channel-2"] [data-testid="monitor-button"]')


      84 |   // 打开监控面板
      85 |   async openMonitorPanel(channelId: string) {
    > 86 |     await this.page.click(`[data-testid="channel-${channelId}"] [data-testid="monitor-button"]`)
         |                     ^
      87 |   }
      88 |
      89 |   // 重连渠道
        at ChannelManagementPage.openMonitorPanel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:86:21)
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:418:25

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-状态监控功能-应该支持手动重连-webkit/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-状态监控功能-应该支持手动重连-webkit/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-状态监控功能-应该支持手动重连-webkit/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-状态监控功能-应该支持手动重连-webkit/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 错误处理场景 › 应该处理网络错误" classname="channel_management.spec.ts" time="5.897">
<failure message="channel_management.spec.ts:433:9 应该处理网络错误" type="FAILURE">
<![CDATA[  [webkit] › channel_management.spec.ts:433:9 › 渠道管理E2E测试 › 错误处理场景 › 应该处理网络错误 ──────────────────────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('[data-testid="network-error"]')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('[data-testid="network-error"]')


      440 |
      441 |       // 验证错误提示
    > 442 |       await expect(page.locator('[data-testid="network-error"]')).toBeVisible()
          |                                                                   ^
      443 |       await expect(page.locator('[data-testid="retry-button"]')).toBeVisible()
      444 |
      445 |       // 点击重试
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:442:67

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理网络错误-webkit/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理网络错误-webkit/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理网络错误-webkit/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理网络错误-webkit/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 错误处理场景 › 应该处理服务器错误" classname="channel_management.spec.ts" time="5.889">
<failure message="channel_management.spec.ts:449:9 应该处理服务器错误" type="FAILURE">
<![CDATA[  [webkit] › channel_management.spec.ts:449:9 › 渠道管理E2E测试 › 错误处理场景 › 应该处理服务器错误 ─────────────────────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('[data-testid="server-error"]')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('[data-testid="server-error"]')


      462 |
      463 |       // 验证错误提示
    > 464 |       await expect(page.locator('[data-testid="server-error"]')).toBeVisible()
          |                                                                  ^
      465 |       await expect(page.locator('[data-testid="error-message"]')).toContainText('服务器内部错误')
      466 |     })
      467 |   })
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:464:66

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理服务器错误-webkit/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理服务器错误-webkit/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理服务器错误-webkit/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理服务器错误-webkit/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 响应式设计测试 › 应该在移动端正确显示" classname="channel_management.spec.ts" time="5.898">
<failure message="channel_management.spec.ts:470:9 应该在移动端正确显示" type="FAILURE">
<![CDATA[  [webkit] › channel_management.spec.ts:470:9 › 渠道管理E2E测试 › 响应式设计测试 › 应该在移动端正确显示 ───────────────────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('[data-testid="mobile-layout"]')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('[data-testid="mobile-layout"]')


      475 |
      476 |       // 验证移动端布局
    > 477 |       await expect(page.locator('[data-testid="mobile-layout"]')).toBeVisible()
          |                                                                   ^
      478 |       await expect(page.locator('[data-testid="mobile-menu"]')).toBeVisible()
      479 |
      480 |       // 验证响应式表格
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:477:67

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在移动端正确显示-webkit/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在移动端正确显示-webkit/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在移动端正确显示-webkit/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在移动端正确显示-webkit/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 响应式设计测试 › 应该在平板端正确显示" classname="channel_management.spec.ts" time="6.002">
<failure message="channel_management.spec.ts:484:9 应该在平板端正确显示" type="FAILURE">
<![CDATA[  [webkit] › channel_management.spec.ts:484:9 › 渠道管理E2E测试 › 响应式设计测试 › 应该在平板端正确显示 ───────────────────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('[data-testid="tablet-layout"]')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('[data-testid="tablet-layout"]')


      489 |
      490 |       // 验证平板端布局
    > 491 |       await expect(page.locator('[data-testid="tablet-layout"]')).toBeVisible()
          |                                                                   ^
      492 |     })
      493 |   })
      494 |
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:491:67

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在平板端正确显示-webkit/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在平板端正确显示-webkit/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在平板端正确显示-webkit/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在平板端正确显示-webkit/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 性能测试 › 页面加载性能应该符合要求" classname="channel_management.spec.ts" time="0.986">
</testcase>
<testcase name="渠道管理E2E测试 › 性能测试 › 大量数据渲染性能" classname="channel_management.spec.ts" time="5.993">
<failure message="channel_management.spec.ts:510:9 大量数据渲染性能" type="FAILURE">
<![CDATA[  [webkit] › channel_management.spec.ts:510:9 › 渠道管理E2E测试 › 性能测试 › 大量数据渲染性能 ────────────────────────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('[data-testid="channel-list"]')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('[data-testid="channel-list"]')


      538 |       
      539 |       // 等待列表渲染完成
    > 540 |       await expect(page.locator('[data-testid="channel-list"]')).toBeVisible()
          |                                                                  ^
      541 |       
      542 |       const renderTime = Date.now() - startTime
      543 |       
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:540:66

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-性能测试-大量数据渲染性能-webkit/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-性能测试-大量数据渲染性能-webkit/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-性能测试-大量数据渲染性能-webkit/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-性能测试-大量数据渲染性能-webkit/error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="channel_management.spec.ts" timestamp="2025-08-03T18:39:01.375Z" hostname="Mobile Chrome" tests="15" failures="14" skipped="0" time="122.564" errors="0">
<testcase name="渠道管理E2E测试 › 渠道连接流程 › 应该成功连接新渠道" classname="channel_management.spec.ts" time="6.637">
<failure message="channel_management.spec.ts:172:9 应该成功连接新渠道" type="FAILURE">
<![CDATA[  [Mobile Chrome] › channel_management.spec.ts:172:9 › 渠道管理E2E测试 › 渠道连接流程 › 应该成功连接新渠道 ──────────────

    Error: Timed out 5000ms waiting for expect(locator).toContainText(expected)

    Locator: locator('h1')
    Expected string: "渠道管理"
    Received: <element(s) not found>
    Call log:
      - Expect "toContainText" with timeout 5000ms
      - waiting for locator('h1')


      174 |
      175 |       // 验证页面加载
    > 176 |       await expect(page.locator('h1')).toContainText('渠道管理')
          |                                        ^
      177 |
      178 |       // 点击添加渠道
      179 |       await channelPage.clickAddChannel()
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:176:40

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该成功连接新渠道-Mobile-Chrome/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该成功连接新渠道-Mobile-Chrome/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该成功连接新渠道-Mobile-Chrome/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该成功连接新渠道-Mobile-Chrome/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 渠道连接流程 › 应该处理无效Cookie" classname="channel_management.spec.ts" time="11.418">
<failure message="channel_management.spec.ts:205:9 应该处理无效Cookie" type="FAILURE">
<![CDATA[  [Mobile Chrome] › channel_management.spec.ts:205:9 › 渠道管理E2E测试 › 渠道连接流程 › 应该处理无效Cookie ───────────

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="add-channel-button"]')


      31 |   // 添加渠道
      32 |   async clickAddChannel() {
    > 33 |     await this.page.click('[data-testid="add-channel-button"]')
         |                     ^
      34 |   }
      35 |
      36 |   // 选择平台
        at ChannelManagementPage.clickAddChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:33:21)
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:208:25

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理无效Cookie-Mobile-Chrome/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理无效Cookie-Mobile-Chrome/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理无效Cookie-Mobile-Chrome/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理无效Cookie-Mobile-Chrome/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 渠道连接流程 › 应该处理重复渠道连接" classname="channel_management.spec.ts" time="11.065">
<failure message="channel_management.spec.ts:222:9 应该处理重复渠道连接" type="FAILURE">
<![CDATA[  [Mobile Chrome] › channel_management.spec.ts:222:9 › 渠道管理E2E测试 › 渠道连接流程 › 应该处理重复渠道连接 ─────────────

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="add-channel-button"]')


      31 |   // 添加渠道
      32 |   async clickAddChannel() {
    > 33 |     await this.page.click('[data-testid="add-channel-button"]')
         |                     ^
      34 |   }
      35 |
      36 |   // 选择平台
        at ChannelManagementPage.clickAddChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:33:21)
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:236:25

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理重复渠道连接-Mobile-Chrome/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理重复渠道连接-Mobile-Chrome/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理重复渠道连接-Mobile-Chrome/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理重复渠道连接-Mobile-Chrome/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 渠道管理操作 › 应该显示渠道列表" classname="channel_management.spec.ts" time="6.043">
<failure message="channel_management.spec.ts:287:9 应该显示渠道列表" type="FAILURE">
<![CDATA[  [Mobile Chrome] › channel_management.spec.ts:287:9 › 渠道管理E2E测试 › 渠道管理操作 › 应该显示渠道列表 ───────────────

    Error: Timed out 5000ms waiting for expect(locator).toHaveCount(expected)

    Locator: locator('[data-testid="channel-list"] [data-testid^="channel-"]')
    Expected: 2
    Received: 0
    Call log:
      - Expect "toHaveCount" with timeout 5000ms
      - waiting for locator('[data-testid="channel-list"] [data-testid^="channel-"]')
        9 × locator resolved to 0 elements
          - unexpected value "0"


      290 |       // 验证渠道列表显示
      291 |       const channels = await channelPage.getChannelList()
    > 292 |       await expect(channels).toHaveCount(2)
          |                              ^
      293 |
      294 |       // 验证渠道信息
      295 |       await expect(page.locator('[data-testid="channel-channel-1"]')).toContainText('主店铺')
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:292:30

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该显示渠道列表-Mobile-Chrome/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该显示渠道列表-Mobile-Chrome/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该显示渠道列表-Mobile-Chrome/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该显示渠道列表-Mobile-Chrome/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 渠道管理操作 › 应该支持搜索功能" classname="channel_management.spec.ts" time="11.212">
<failure message="channel_management.spec.ts:303:9 应该支持搜索功能" type="FAILURE">
<![CDATA[  [Mobile Chrome] › channel_management.spec.ts:303:9 › 渠道管理E2E测试 › 渠道管理操作 › 应该支持搜索功能 ───────────────

    TimeoutError: page.fill: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="search-input"]')


      61 |   // 搜索渠道
      62 |   async searchChannel(query: string) {
    > 63 |     await this.page.fill('[data-testid="search-input"]', query)
         |                     ^
      64 |     await this.page.keyboard.press('Enter')
      65 |   }
      66 |
        at ChannelManagementPage.searchChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:63:21)
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:307:25

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持搜索功能-Mobile-Chrome/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持搜索功能-Mobile-Chrome/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持搜索功能-Mobile-Chrome/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持搜索功能-Mobile-Chrome/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 渠道管理操作 › 应该支持状态筛选" classname="channel_management.spec.ts" time="11.138">
<failure message="channel_management.spec.ts:314:9 应该支持状态筛选" type="FAILURE">
<![CDATA[  [Mobile Chrome] › channel_management.spec.ts:314:9 › 渠道管理E2E测试 › 渠道管理操作 › 应该支持状态筛选 ───────────────

    TimeoutError: page.selectOption: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="status-filter"]')


      67 |   // 筛选渠道
      68 |   async filterByStatus(status: string) {
    > 69 |     await this.page.selectOption('[data-testid="status-filter"]', status)
         |                     ^
      70 |   }
      71 |
      72 |   // 删除渠道
        at ChannelManagementPage.filterByStatus (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:69:21)
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:318:25

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持状态筛选-Mobile-Chrome/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持状态筛选-Mobile-Chrome/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持状态筛选-Mobile-Chrome/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持状态筛选-Mobile-Chrome/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 渠道管理操作 › 应该支持删除和恢复渠道" classname="channel_management.spec.ts" time="11.125">
<failure message="channel_management.spec.ts:325:9 应该支持删除和恢复渠道" type="FAILURE">
<![CDATA[  [Mobile Chrome] › channel_management.spec.ts:325:9 › 渠道管理E2E测试 › 渠道管理操作 › 应该支持删除和恢复渠道 ────────────

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="channel-channel-1"] [data-testid="delete-button"]')


      72 |   // 删除渠道
      73 |   async deleteChannel(channelId: string) {
    > 74 |     await this.page.click(`[data-testid="channel-${channelId}"] [data-testid="delete-button"]`)
         |                     ^
      75 |     await this.page.click('[data-testid="confirm-delete"]')
      76 |   }
      77 |
        at ChannelManagementPage.deleteChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:74:21)
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:348:25

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持删除和恢复渠道-Mobile-Chrome/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持删除和恢复渠道-Mobile-Chrome/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持删除和恢复渠道-Mobile-Chrome/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持删除和恢复渠道-Mobile-Chrome/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 状态监控功能 › 应该显示实时状态监控" classname="channel_management.spec.ts" time="11.076">
<failure message="channel_management.spec.ts:365:9 应该显示实时状态监控" type="FAILURE">
<![CDATA[  [Mobile Chrome] › channel_management.spec.ts:365:9 › 渠道管理E2E测试 › 状态监控功能 › 应该显示实时状态监控 ─────────────

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="channel-channel-1"] [data-testid="monitor-button"]')


      84 |   // 打开监控面板
      85 |   async openMonitorPanel(channelId: string) {
    > 86 |     await this.page.click(`[data-testid="channel-${channelId}"] [data-testid="monitor-button"]`)
         |                     ^
      87 |   }
      88 |
      89 |   // 重连渠道
        at ChannelManagementPage.openMonitorPanel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:86:21)
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:401:25

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-状态监控功能-应该显示实时状态监控-Mobile-Chrome/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-状态监控功能-应该显示实时状态监控-Mobile-Chrome/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-状态监控功能-应该显示实时状态监控-Mobile-Chrome/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-状态监控功能-应该显示实时状态监控-Mobile-Chrome/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 状态监控功能 › 应该支持手动重连" classname="channel_management.spec.ts" time="11.048">
<failure message="channel_management.spec.ts:411:9 应该支持手动重连" type="FAILURE">
<![CDATA[  [Mobile Chrome] › channel_management.spec.ts:411:9 › 渠道管理E2E测试 › 状态监控功能 › 应该支持手动重连 ───────────────

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="channel-channel-2"] [data-testid="monitor-button"]')


      84 |   // 打开监控面板
      85 |   async openMonitorPanel(channelId: string) {
    > 86 |     await this.page.click(`[data-testid="channel-${channelId}"] [data-testid="monitor-button"]`)
         |                     ^
      87 |   }
      88 |
      89 |   // 重连渠道
        at ChannelManagementPage.openMonitorPanel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:86:21)
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:418:25

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-状态监控功能-应该支持手动重连-Mobile-Chrome/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-状态监控功能-应该支持手动重连-Mobile-Chrome/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-状态监控功能-应该支持手动重连-Mobile-Chrome/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-状态监控功能-应该支持手动重连-Mobile-Chrome/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 错误处理场景 › 应该处理网络错误" classname="channel_management.spec.ts" time="6.148">
<failure message="channel_management.spec.ts:433:9 应该处理网络错误" type="FAILURE">
<![CDATA[  [Mobile Chrome] › channel_management.spec.ts:433:9 › 渠道管理E2E测试 › 错误处理场景 › 应该处理网络错误 ───────────────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('[data-testid="network-error"]')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('[data-testid="network-error"]')


      440 |
      441 |       // 验证错误提示
    > 442 |       await expect(page.locator('[data-testid="network-error"]')).toBeVisible()
          |                                                                   ^
      443 |       await expect(page.locator('[data-testid="retry-button"]')).toBeVisible()
      444 |
      445 |       // 点击重试
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:442:67

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理网络错误-Mobile-Chrome/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理网络错误-Mobile-Chrome/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理网络错误-Mobile-Chrome/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理网络错误-Mobile-Chrome/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 错误处理场景 › 应该处理服务器错误" classname="channel_management.spec.ts" time="6.017">
<failure message="channel_management.spec.ts:449:9 应该处理服务器错误" type="FAILURE">
<![CDATA[  [Mobile Chrome] › channel_management.spec.ts:449:9 › 渠道管理E2E测试 › 错误处理场景 › 应该处理服务器错误 ──────────────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('[data-testid="server-error"]')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('[data-testid="server-error"]')


      462 |
      463 |       // 验证错误提示
    > 464 |       await expect(page.locator('[data-testid="server-error"]')).toBeVisible()
          |                                                                  ^
      465 |       await expect(page.locator('[data-testid="error-message"]')).toContainText('服务器内部错误')
      466 |     })
      467 |   })
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:464:66

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理服务器错误-Mobile-Chrome/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理服务器错误-Mobile-Chrome/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理服务器错误-Mobile-Chrome/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理服务器错误-Mobile-Chrome/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 响应式设计测试 › 应该在移动端正确显示" classname="channel_management.spec.ts" time="6.081">
<failure message="channel_management.spec.ts:470:9 应该在移动端正确显示" type="FAILURE">
<![CDATA[  [Mobile Chrome] › channel_management.spec.ts:470:9 › 渠道管理E2E测试 › 响应式设计测试 › 应该在移动端正确显示 ────────────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('[data-testid="mobile-layout"]')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('[data-testid="mobile-layout"]')


      475 |
      476 |       // 验证移动端布局
    > 477 |       await expect(page.locator('[data-testid="mobile-layout"]')).toBeVisible()
          |                                                                   ^
      478 |       await expect(page.locator('[data-testid="mobile-menu"]')).toBeVisible()
      479 |
      480 |       // 验证响应式表格
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:477:67

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在移动端正确显示-Mobile-Chrome/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在移动端正确显示-Mobile-Chrome/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在移动端正确显示-Mobile-Chrome/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在移动端正确显示-Mobile-Chrome/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 响应式设计测试 › 应该在平板端正确显示" classname="channel_management.spec.ts" time="6.256">
<failure message="channel_management.spec.ts:484:9 应该在平板端正确显示" type="FAILURE">
<![CDATA[  [Mobile Chrome] › channel_management.spec.ts:484:9 › 渠道管理E2E测试 › 响应式设计测试 › 应该在平板端正确显示 ────────────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('[data-testid="tablet-layout"]')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('[data-testid="tablet-layout"]')


      489 |
      490 |       // 验证平板端布局
    > 491 |       await expect(page.locator('[data-testid="tablet-layout"]')).toBeVisible()
          |                                                                   ^
      492 |     })
      493 |   })
      494 |
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:491:67

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在平板端正确显示-Mobile-Chrome/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在平板端正确显示-Mobile-Chrome/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在平板端正确显示-Mobile-Chrome/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在平板端正确显示-Mobile-Chrome/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 性能测试 › 页面加载性能应该符合要求" classname="channel_management.spec.ts" time="1.182">
</testcase>
<testcase name="渠道管理E2E测试 › 性能测试 › 大量数据渲染性能" classname="channel_management.spec.ts" time="6.118">
<failure message="channel_management.spec.ts:510:9 大量数据渲染性能" type="FAILURE">
<![CDATA[  [Mobile Chrome] › channel_management.spec.ts:510:9 › 渠道管理E2E测试 › 性能测试 › 大量数据渲染性能 ─────────────────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('[data-testid="channel-list"]')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('[data-testid="channel-list"]')


      538 |       
      539 |       // 等待列表渲染完成
    > 540 |       await expect(page.locator('[data-testid="channel-list"]')).toBeVisible()
          |                                                                  ^
      541 |       
      542 |       const renderTime = Date.now() - startTime
      543 |       
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:540:66

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-性能测试-大量数据渲染性能-Mobile-Chrome/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-性能测试-大量数据渲染性能-Mobile-Chrome/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-性能测试-大量数据渲染性能-Mobile-Chrome/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-性能测试-大量数据渲染性能-Mobile-Chrome/error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="channel_management.spec.ts" timestamp="2025-08-03T18:39:01.375Z" hostname="Mobile Safari" tests="15" failures="14" skipped="0" time="119.654" errors="0">
<testcase name="渠道管理E2E测试 › 渠道连接流程 › 应该成功连接新渠道" classname="channel_management.spec.ts" time="6.231">
<failure message="channel_management.spec.ts:172:9 应该成功连接新渠道" type="FAILURE">
<![CDATA[  [Mobile Safari] › channel_management.spec.ts:172:9 › 渠道管理E2E测试 › 渠道连接流程 › 应该成功连接新渠道 ──────────────

    Error: Timed out 5000ms waiting for expect(locator).toContainText(expected)

    Locator: locator('h1')
    Expected string: "渠道管理"
    Received: <element(s) not found>
    Call log:
      - Expect "toContainText" with timeout 5000ms
      - waiting for locator('h1')


      174 |
      175 |       // 验证页面加载
    > 176 |       await expect(page.locator('h1')).toContainText('渠道管理')
          |                                        ^
      177 |
      178 |       // 点击添加渠道
      179 |       await channelPage.clickAddChannel()
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:176:40

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该成功连接新渠道-Mobile-Safari/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该成功连接新渠道-Mobile-Safari/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该成功连接新渠道-Mobile-Safari/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该成功连接新渠道-Mobile-Safari/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 渠道连接流程 › 应该处理无效Cookie" classname="channel_management.spec.ts" time="11.219">
<failure message="channel_management.spec.ts:205:9 应该处理无效Cookie" type="FAILURE">
<![CDATA[  [Mobile Safari] › channel_management.spec.ts:205:9 › 渠道管理E2E测试 › 渠道连接流程 › 应该处理无效Cookie ───────────

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="add-channel-button"]')


      31 |   // 添加渠道
      32 |   async clickAddChannel() {
    > 33 |     await this.page.click('[data-testid="add-channel-button"]')
         |                     ^
      34 |   }
      35 |
      36 |   // 选择平台
        at ChannelManagementPage.clickAddChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:33:21)
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:208:25

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理无效Cookie-Mobile-Safari/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理无效Cookie-Mobile-Safari/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理无效Cookie-Mobile-Safari/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理无效Cookie-Mobile-Safari/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 渠道连接流程 › 应该处理重复渠道连接" classname="channel_management.spec.ts" time="10.924">
<failure message="channel_management.spec.ts:222:9 应该处理重复渠道连接" type="FAILURE">
<![CDATA[  [Mobile Safari] › channel_management.spec.ts:222:9 › 渠道管理E2E测试 › 渠道连接流程 › 应该处理重复渠道连接 ─────────────

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="add-channel-button"]')


      31 |   // 添加渠道
      32 |   async clickAddChannel() {
    > 33 |     await this.page.click('[data-testid="add-channel-button"]')
         |                     ^
      34 |   }
      35 |
      36 |   // 选择平台
        at ChannelManagementPage.clickAddChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:33:21)
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:236:25

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理重复渠道连接-Mobile-Safari/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理重复渠道连接-Mobile-Safari/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理重复渠道连接-Mobile-Safari/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理重复渠道连接-Mobile-Safari/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 渠道管理操作 › 应该显示渠道列表" classname="channel_management.spec.ts" time="5.933">
<failure message="channel_management.spec.ts:287:9 应该显示渠道列表" type="FAILURE">
<![CDATA[  [Mobile Safari] › channel_management.spec.ts:287:9 › 渠道管理E2E测试 › 渠道管理操作 › 应该显示渠道列表 ───────────────

    Error: Timed out 5000ms waiting for expect(locator).toHaveCount(expected)

    Locator: locator('[data-testid="channel-list"] [data-testid^="channel-"]')
    Expected: 2
    Received: 0
    Call log:
      - Expect "toHaveCount" with timeout 5000ms
      - waiting for locator('[data-testid="channel-list"] [data-testid^="channel-"]')
        9 × locator resolved to 0 elements
          - unexpected value "0"


      290 |       // 验证渠道列表显示
      291 |       const channels = await channelPage.getChannelList()
    > 292 |       await expect(channels).toHaveCount(2)
          |                              ^
      293 |
      294 |       // 验证渠道信息
      295 |       await expect(page.locator('[data-testid="channel-channel-1"]')).toContainText('主店铺')
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:292:30

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该显示渠道列表-Mobile-Safari/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该显示渠道列表-Mobile-Safari/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该显示渠道列表-Mobile-Safari/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该显示渠道列表-Mobile-Safari/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 渠道管理操作 › 应该支持搜索功能" classname="channel_management.spec.ts" time="10.939">
<failure message="channel_management.spec.ts:303:9 应该支持搜索功能" type="FAILURE">
<![CDATA[  [Mobile Safari] › channel_management.spec.ts:303:9 › 渠道管理E2E测试 › 渠道管理操作 › 应该支持搜索功能 ───────────────

    TimeoutError: page.fill: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="search-input"]')


      61 |   // 搜索渠道
      62 |   async searchChannel(query: string) {
    > 63 |     await this.page.fill('[data-testid="search-input"]', query)
         |                     ^
      64 |     await this.page.keyboard.press('Enter')
      65 |   }
      66 |
        at ChannelManagementPage.searchChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:63:21)
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:307:25

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持搜索功能-Mobile-Safari/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持搜索功能-Mobile-Safari/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持搜索功能-Mobile-Safari/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持搜索功能-Mobile-Safari/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 渠道管理操作 › 应该支持状态筛选" classname="channel_management.spec.ts" time="10.944">
<failure message="channel_management.spec.ts:314:9 应该支持状态筛选" type="FAILURE">
<![CDATA[  [Mobile Safari] › channel_management.spec.ts:314:9 › 渠道管理E2E测试 › 渠道管理操作 › 应该支持状态筛选 ───────────────

    TimeoutError: page.selectOption: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="status-filter"]')


      67 |   // 筛选渠道
      68 |   async filterByStatus(status: string) {
    > 69 |     await this.page.selectOption('[data-testid="status-filter"]', status)
         |                     ^
      70 |   }
      71 |
      72 |   // 删除渠道
        at ChannelManagementPage.filterByStatus (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:69:21)
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:318:25

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持状态筛选-Mobile-Safari/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持状态筛选-Mobile-Safari/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持状态筛选-Mobile-Safari/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持状态筛选-Mobile-Safari/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 渠道管理操作 › 应该支持删除和恢复渠道" classname="channel_management.spec.ts" time="10.968">
<failure message="channel_management.spec.ts:325:9 应该支持删除和恢复渠道" type="FAILURE">
<![CDATA[  [Mobile Safari] › channel_management.spec.ts:325:9 › 渠道管理E2E测试 › 渠道管理操作 › 应该支持删除和恢复渠道 ────────────

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="channel-channel-1"] [data-testid="delete-button"]')


      72 |   // 删除渠道
      73 |   async deleteChannel(channelId: string) {
    > 74 |     await this.page.click(`[data-testid="channel-${channelId}"] [data-testid="delete-button"]`)
         |                     ^
      75 |     await this.page.click('[data-testid="confirm-delete"]')
      76 |   }
      77 |
        at ChannelManagementPage.deleteChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:74:21)
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:348:25

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持删除和恢复渠道-Mobile-Safari/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持删除和恢复渠道-Mobile-Safari/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持删除和恢复渠道-Mobile-Safari/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持删除和恢复渠道-Mobile-Safari/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 状态监控功能 › 应该显示实时状态监控" classname="channel_management.spec.ts" time="10.931">
<failure message="channel_management.spec.ts:365:9 应该显示实时状态监控" type="FAILURE">
<![CDATA[  [Mobile Safari] › channel_management.spec.ts:365:9 › 渠道管理E2E测试 › 状态监控功能 › 应该显示实时状态监控 ─────────────

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="channel-channel-1"] [data-testid="monitor-button"]')


      84 |   // 打开监控面板
      85 |   async openMonitorPanel(channelId: string) {
    > 86 |     await this.page.click(`[data-testid="channel-${channelId}"] [data-testid="monitor-button"]`)
         |                     ^
      87 |   }
      88 |
      89 |   // 重连渠道
        at ChannelManagementPage.openMonitorPanel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:86:21)
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:401:25

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-状态监控功能-应该显示实时状态监控-Mobile-Safari/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-状态监控功能-应该显示实时状态监控-Mobile-Safari/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-状态监控功能-应该显示实时状态监控-Mobile-Safari/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-状态监控功能-应该显示实时状态监控-Mobile-Safari/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 状态监控功能 › 应该支持手动重连" classname="channel_management.spec.ts" time="10.901">
<failure message="channel_management.spec.ts:411:9 应该支持手动重连" type="FAILURE">
<![CDATA[  [Mobile Safari] › channel_management.spec.ts:411:9 › 渠道管理E2E测试 › 状态监控功能 › 应该支持手动重连 ───────────────

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('[data-testid="channel-channel-2"] [data-testid="monitor-button"]')


      84 |   // 打开监控面板
      85 |   async openMonitorPanel(channelId: string) {
    > 86 |     await this.page.click(`[data-testid="channel-${channelId}"] [data-testid="monitor-button"]`)
         |                     ^
      87 |   }
      88 |
      89 |   // 重连渠道
        at ChannelManagementPage.openMonitorPanel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:86:21)
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:418:25

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-状态监控功能-应该支持手动重连-Mobile-Safari/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-状态监控功能-应该支持手动重连-Mobile-Safari/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-状态监控功能-应该支持手动重连-Mobile-Safari/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-状态监控功能-应该支持手动重连-Mobile-Safari/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 错误处理场景 › 应该处理网络错误" classname="channel_management.spec.ts" time="5.914">
<failure message="channel_management.spec.ts:433:9 应该处理网络错误" type="FAILURE">
<![CDATA[  [Mobile Safari] › channel_management.spec.ts:433:9 › 渠道管理E2E测试 › 错误处理场景 › 应该处理网络错误 ───────────────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('[data-testid="network-error"]')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('[data-testid="network-error"]')


      440 |
      441 |       // 验证错误提示
    > 442 |       await expect(page.locator('[data-testid="network-error"]')).toBeVisible()
          |                                                                   ^
      443 |       await expect(page.locator('[data-testid="retry-button"]')).toBeVisible()
      444 |
      445 |       // 点击重试
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:442:67

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理网络错误-Mobile-Safari/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理网络错误-Mobile-Safari/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理网络错误-Mobile-Safari/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理网络错误-Mobile-Safari/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 错误处理场景 › 应该处理服务器错误" classname="channel_management.spec.ts" time="5.9">
<failure message="channel_management.spec.ts:449:9 应该处理服务器错误" type="FAILURE">
<![CDATA[  [Mobile Safari] › channel_management.spec.ts:449:9 › 渠道管理E2E测试 › 错误处理场景 › 应该处理服务器错误 ──────────────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('[data-testid="server-error"]')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('[data-testid="server-error"]')


      462 |
      463 |       // 验证错误提示
    > 464 |       await expect(page.locator('[data-testid="server-error"]')).toBeVisible()
          |                                                                  ^
      465 |       await expect(page.locator('[data-testid="error-message"]')).toContainText('服务器内部错误')
      466 |     })
      467 |   })
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:464:66

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理服务器错误-Mobile-Safari/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理服务器错误-Mobile-Safari/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理服务器错误-Mobile-Safari/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理服务器错误-Mobile-Safari/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 响应式设计测试 › 应该在移动端正确显示" classname="channel_management.spec.ts" time="5.962">
<failure message="channel_management.spec.ts:470:9 应该在移动端正确显示" type="FAILURE">
<![CDATA[  [Mobile Safari] › channel_management.spec.ts:470:9 › 渠道管理E2E测试 › 响应式设计测试 › 应该在移动端正确显示 ────────────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('[data-testid="mobile-layout"]')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('[data-testid="mobile-layout"]')


      475 |
      476 |       // 验证移动端布局
    > 477 |       await expect(page.locator('[data-testid="mobile-layout"]')).toBeVisible()
          |                                                                   ^
      478 |       await expect(page.locator('[data-testid="mobile-menu"]')).toBeVisible()
      479 |
      480 |       // 验证响应式表格
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:477:67

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在移动端正确显示-Mobile-Safari/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在移动端正确显示-Mobile-Safari/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在移动端正确显示-Mobile-Safari/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在移动端正确显示-Mobile-Safari/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 响应式设计测试 › 应该在平板端正确显示" classname="channel_management.spec.ts" time="5.979">
<failure message="channel_management.spec.ts:484:9 应该在平板端正确显示" type="FAILURE">
<![CDATA[  [Mobile Safari] › channel_management.spec.ts:484:9 › 渠道管理E2E测试 › 响应式设计测试 › 应该在平板端正确显示 ────────────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('[data-testid="tablet-layout"]')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('[data-testid="tablet-layout"]')


      489 |
      490 |       // 验证平板端布局
    > 491 |       await expect(page.locator('[data-testid="tablet-layout"]')).toBeVisible()
          |                                                                   ^
      492 |     })
      493 |   })
      494 |
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:491:67

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在平板端正确显示-Mobile-Safari/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在平板端正确显示-Mobile-Safari/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在平板端正确显示-Mobile-Safari/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在平板端正确显示-Mobile-Safari/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="渠道管理E2E测试 › 性能测试 › 页面加载性能应该符合要求" classname="channel_management.spec.ts" time="0.937">
</testcase>
<testcase name="渠道管理E2E测试 › 性能测试 › 大量数据渲染性能" classname="channel_management.spec.ts" time="5.972">
<failure message="channel_management.spec.ts:510:9 大量数据渲染性能" type="FAILURE">
<![CDATA[  [Mobile Safari] › channel_management.spec.ts:510:9 › 渠道管理E2E测试 › 性能测试 › 大量数据渲染性能 ─────────────────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('[data-testid="channel-list"]')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('[data-testid="channel-list"]')


      538 |       
      539 |       // 等待列表渲染完成
    > 540 |       await expect(page.locator('[data-testid="channel-list"]')).toBeVisible()
          |                                                                  ^
      541 |       
      542 |       const renderTime = Date.now() - startTime
      543 |       
        at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:540:66

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ../test-results/channel_management-渠道管理E2E测试-性能测试-大量数据渲染性能-Mobile-Safari/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/channel_management-渠道管理E2E测试-性能测试-大量数据渲染性能-Mobile-Safari/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-性能测试-大量数据渲染性能-Mobile-Safari/test-failed-1.png]]

[[ATTACHMENT|test-results/channel_management-渠道管理E2E测试-性能测试-大量数据渲染性能-Mobile-Safari/error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
</testsuites>