/**
 * 渠道管理E2E测试
 * 
 * 基于BDD剧本的端到端测试，覆盖：
 * - 渠道连接完整流程
 * - 渠道管理操作
 * - 状态监控功能
 * - 错误处理场景
 */

import { test, expect, Page } from '@playwright/test'

// 测试数据
const testData = {
  validCookie: 'test_cookie_value_12345',
  invalidCookie: 'invalid_cookie',
  channelName: '测试店铺',
  platformAccountId: 'test_shop_123'
}

// 页面对象模式
class ChannelManagementPage {
  constructor(private page: Page) {}

  // 导航到页面
  async goto() {
    await this.page.goto('/channel-management')
    await this.page.waitForLoadState('networkidle')
  }

  // 添加渠道
  async clickAddChannel() {
    await this.page.click('[data-testid="add-channel-button"]')
  }

  // 选择平台
  async selectPlatform(platform: string) {
    await this.page.click(`[data-testid="platform-${platform}"]`)
  }

  // 输入Cookie
  async enterCookie(cookie: string) {
    await this.page.fill('[data-testid="cookie-input"]', cookie)
  }

  // 点击连接按钮
  async clickConnect() {
    await this.page.click('[data-testid="connect-button"]')
  }

  // 等待连接结果
  async waitForConnectionResult() {
    await this.page.waitForSelector('[data-testid="connection-result"]', { timeout: 10000 })
  }

  // 获取渠道列表
  async getChannelList() {
    return this.page.locator('[data-testid="channel-list"] [data-testid^="channel-"]')
  }

  // 搜索渠道
  async searchChannel(query: string) {
    await this.page.fill('[data-testid="search-input"]', query)
    await this.page.keyboard.press('Enter')
  }

  // 筛选渠道
  async filterByStatus(status: string) {
    await this.page.selectOption('[data-testid="status-filter"]', status)
  }

  // 删除渠道
  async deleteChannel(channelId: string) {
    await this.page.click(`[data-testid="channel-${channelId}"] [data-testid="delete-button"]`)
    await this.page.click('[data-testid="confirm-delete"]')
  }

  // 恢复渠道
  async restoreChannel(channelId: string) {
    await this.page.click('[data-testid="history-tab"]')
    await this.page.click(`[data-testid="channel-${channelId}"] [data-testid="restore-button"]`)
  }

  // 打开监控面板
  async openMonitorPanel(channelId: string) {
    await this.page.click(`[data-testid="channel-${channelId}"] [data-testid="monitor-button"]`)
  }

  // 重连渠道
  async reconnectChannel() {
    await this.page.click('[data-testid="reconnect-button"]')
  }
}

test.describe('渠道管理E2E测试', () => {
  let page: Page
  let channelPage: ChannelManagementPage

  test.beforeEach(async ({ browser }) => {
    page = await browser.newPage()
    channelPage = new ChannelManagementPage(page)
    
    // 设置API拦截
    await page.route('**/api/v1/channels/validate-cookie', async (route) => {
      const request = route.request()
      const postData = request.postDataJSON()
      
      if (postData.cookie === testData.validCookie) {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            valid: true,
            accountInfo: {
              id: testData.platformAccountId,
              name: testData.channelName,
              avatar: 'https://example.com/avatar.jpg'
            }
          })
        })
      } else {
        await route.fulfill({
          status: 400,
          contentType: 'application/json',
          body: JSON.stringify({
            valid: false,
            error: 'Cookie无效或已过期'
          })
        })
      }
    })

    await page.route('**/api/v1/channels/connect', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          channel: {
            id: 'new-channel-id',
            platform: 'xianyu',
            platformAccountId: testData.platformAccountId,
            displayName: testData.channelName,
            status: 'connected',
            isDeleted: false,
            lastConnectedAt: new Date().toISOString(),
            lastActiveAt: new Date().toISOString(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          }
        })
      })
    })

    await page.route('**/api/v1/channels', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          channels: [],
          total: 0
        })
      })
    })
  })

  test.afterEach(async () => {
    await page.close()
  })

  test.describe('渠道连接流程', () => {
    test('应该成功连接新渠道', async () => {
      await channelPage.goto()

      // 验证页面加载
      await expect(page.locator('h1')).toContainText('渠道管理')

      // 点击添加渠道
      await channelPage.clickAddChannel()
      await expect(page.locator('[data-testid="platform-selection-modal"]')).toBeVisible()

      // 选择闲鱼平台
      await channelPage.selectPlatform('xianyu')
      await expect(page.locator('[data-testid="channel-connection-modal"]')).toBeVisible()

      // 输入有效Cookie
      await channelPage.enterCookie(testData.validCookie)

      // 验证Cookie自动验证
      await expect(page.locator('[data-testid="validation-success"]')).toBeVisible()
      await expect(page.locator('[data-testid="account-info"]')).toContainText(testData.channelName)

      // 点击连接
      await channelPage.clickConnect()

      // 等待连接完成
      await channelPage.waitForConnectionResult()
      await expect(page.locator('[data-testid="connection-success"]')).toBeVisible()

      // 验证渠道列表更新
      const channels = await channelPage.getChannelList()
      await expect(channels).toHaveCount(1)
    })

    test('应该处理无效Cookie', async () => {
      await channelPage.goto()

      await channelPage.clickAddChannel()
      await channelPage.selectPlatform('xianyu')

      // 输入无效Cookie
      await channelPage.enterCookie(testData.invalidCookie)

      // 验证错误提示
      await expect(page.locator('[data-testid="validation-error"]')).toBeVisible()
      await expect(page.locator('[data-testid="validation-error"]')).toContainText('Cookie无效或已过期')

      // 连接按钮应该被禁用
      await expect(page.locator('[data-testid="connect-button"]')).toBeDisabled()
    })

    test('应该处理重复渠道连接', async () => {
      // 设置重复渠道响应
      await page.route('**/api/v1/channels/connect', async (route) => {
        await route.fulfill({
          status: 409,
          contentType: 'application/json',
          body: JSON.stringify({
            code: 'DUPLICATE_CHANNEL',
            message: '该账号已经连接过了'
          })
        })
      })

      await channelPage.goto()
      await channelPage.clickAddChannel()
      await channelPage.selectPlatform('xianyu')
      await channelPage.enterCookie(testData.validCookie)
      await channelPage.clickConnect()

      // 验证重复连接错误
      await expect(page.locator('[data-testid="duplicate-error"]')).toBeVisible()
      await expect(page.locator('[data-testid="duplicate-error"]')).toContainText('该账号已经连接过了')
    })
  })

  test.describe('渠道管理操作', () => {
    test.beforeEach(async () => {
      // 设置已有渠道数据
      await page.route('**/api/v1/channels', async (route) => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            channels: [
              {
                id: 'channel-1',
                platform: 'xianyu',
                platformAccountId: 'shop_123',
                displayName: '主店铺',
                status: 'connected',
                isDeleted: false,
                lastConnectedAt: '2025-08-01T10:30:00Z',
                lastActiveAt: '2025-08-01T15:45:00Z',
                createdAt: '2025-08-01T10:30:00Z',
                updatedAt: '2025-08-01T15:45:00Z'
              },
              {
                id: 'channel-2',
                platform: 'xianyu',
                platformAccountId: 'shop_456',
                displayName: '客服店铺',
                status: 'disconnected',
                isDeleted: false,
                lastConnectedAt: '2025-08-02T14:20:00Z',
                lastActiveAt: '2025-08-02T16:30:00Z',
                createdAt: '2025-08-02T14:20:00Z',
                updatedAt: '2025-08-02T16:30:00Z'
              }
            ],
            total: 2
          })
        })
      })
    })

    test('应该显示渠道列表', async () => {
      await channelPage.goto()

      // 验证渠道列表显示
      const channels = await channelPage.getChannelList()
      await expect(channels).toHaveCount(2)

      // 验证渠道信息
      await expect(page.locator('[data-testid="channel-channel-1"]')).toContainText('主店铺')
      await expect(page.locator('[data-testid="channel-channel-2"]')).toContainText('客服店铺')

      // 验证状态显示
      await expect(page.locator('[data-testid="channel-channel-1"] [data-testid="status-chip"]')).toContainText('已连接')
      await expect(page.locator('[data-testid="channel-channel-2"] [data-testid="status-chip"]')).toContainText('已断开')
    })

    test('应该支持搜索功能', async () => {
      await channelPage.goto()

      // 搜索渠道
      await channelPage.searchChannel('主店铺')

      // 验证搜索结果
      await expect(page.locator('[data-testid="channel-channel-1"]')).toBeVisible()
      await expect(page.locator('[data-testid="channel-channel-2"]')).not.toBeVisible()
    })

    test('应该支持状态筛选', async () => {
      await channelPage.goto()

      // 筛选已连接的渠道
      await channelPage.filterByStatus('connected')

      // 验证筛选结果
      await expect(page.locator('[data-testid="channel-channel-1"]')).toBeVisible()
      await expect(page.locator('[data-testid="channel-channel-2"]')).not.toBeVisible()
    })

    test('应该支持删除和恢复渠道', async () => {
      // 设置删除API响应
      await page.route('**/api/v1/channels/channel-1', async (route) => {
        if (route.request().method() === 'DELETE') {
          await route.fulfill({ status: 200 })
        }
      })

      // 设置恢复API响应
      await page.route('**/api/v1/channels/channel-1/restore', async (route) => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            id: 'channel-1',
            isDeleted: false
          })
        })
      })

      await channelPage.goto()

      // 删除渠道
      await channelPage.deleteChannel('channel-1')

      // 验证删除成功提示
      await expect(page.locator('[data-testid="delete-success"]')).toBeVisible()

      // 切换到历史渠道
      await page.click('[data-testid="history-tab"]')

      // 恢复渠道
      await channelPage.restoreChannel('channel-1')

      // 验证恢复成功提示
      await expect(page.locator('[data-testid="restore-success"]')).toBeVisible()
    })
  })

  test.describe('状态监控功能', () => {
    test('应该显示实时状态监控', async () => {
      // 设置WebSocket模拟
      await page.addInitScript(() => {
        class MockWebSocket {
          constructor(url: string) {
            setTimeout(() => {
              this.onopen?.({} as Event)
              
              // 模拟状态更新消息
              setTimeout(() => {
                this.onmessage?.({
                  data: JSON.stringify({
                    type: 'status_change',
                    channelId: 'channel-1',
                    status: 'connected',
                    timestamp: new Date().toISOString()
                  })
                } as MessageEvent)
              }, 1000)
            }, 100)
          }
          
          send() {}
          close() {}
          onopen?: (event: Event) => void
          onmessage?: (event: MessageEvent) => void
          onerror?: (event: Event) => void
          onclose?: (event: CloseEvent) => void
        }
        
        (window as any).WebSocket = MockWebSocket
      })

      await channelPage.goto()

      // 打开监控面板
      await channelPage.openMonitorPanel('channel-1')

      // 验证监控界面
      await expect(page.locator('[data-testid="channel-monitor"]')).toBeVisible()
      await expect(page.locator('[data-testid="monitoring-status"]')).toContainText('监控中')

      // 验证实时状态更新
      await expect(page.locator('[data-testid="status-indicator"]')).toContainText('已连接')
    })

    test('应该支持手动重连', async () => {
      // 设置重连API响应
      await page.route('**/api/v1/channels/channel-2/reconnect', async (route) => {
        await route.fulfill({ status: 200 })
      })

      await channelPage.goto()
      await channelPage.openMonitorPanel('channel-2')

      // 点击重连按钮
      await channelPage.reconnectChannel()

      // 验证重连进度
      await expect(page.locator('[data-testid="reconnect-spinner"]')).toBeVisible()
      await expect(page.locator('[data-testid="reconnect-status"]')).toContainText('重连中')

      // 等待重连完成
      await expect(page.locator('[data-testid="reconnect-success"]')).toBeVisible()
    })
  })

  test.describe('错误处理场景', () => {
    test('应该处理网络错误', async () => {
      // 模拟网络错误
      await page.route('**/api/v1/channels', async (route) => {
        await route.abort('failed')
      })

      await channelPage.goto()

      // 验证错误提示
      await expect(page.locator('[data-testid="network-error"]')).toBeVisible()
      await expect(page.locator('[data-testid="retry-button"]')).toBeVisible()

      // 点击重试
      await page.click('[data-testid="retry-button"]')
    })

    test('应该处理服务器错误', async () => {
      // 模拟服务器错误
      await page.route('**/api/v1/channels', async (route) => {
        await route.fulfill({
          status: 500,
          contentType: 'application/json',
          body: JSON.stringify({
            error: '服务器内部错误'
          })
        })
      })

      await channelPage.goto()

      // 验证错误提示
      await expect(page.locator('[data-testid="server-error"]')).toBeVisible()
      await expect(page.locator('[data-testid="error-message"]')).toContainText('服务器内部错误')
    })
  })

  test.describe('响应式设计测试', () => {
    test('应该在移动端正确显示', async () => {
      // 设置移动端视口
      await page.setViewportSize({ width: 375, height: 667 })

      await channelPage.goto()

      // 验证移动端布局
      await expect(page.locator('[data-testid="mobile-layout"]')).toBeVisible()
      await expect(page.locator('[data-testid="mobile-menu"]')).toBeVisible()

      // 验证响应式表格
      await expect(page.locator('[data-testid="mobile-channel-card"]')).toBeVisible()
    })

    test('应该在平板端正确显示', async () => {
      // 设置平板端视口
      await page.setViewportSize({ width: 768, height: 1024 })

      await channelPage.goto()

      // 验证平板端布局
      await expect(page.locator('[data-testid="tablet-layout"]')).toBeVisible()
    })
  })

  test.describe('性能测试', () => {
    test('页面加载性能应该符合要求', async () => {
      const startTime = Date.now()
      
      await channelPage.goto()
      
      // 等待页面完全加载
      await page.waitForLoadState('networkidle')
      
      const loadTime = Date.now() - startTime
      
      // 验证加载时间小于3秒
      expect(loadTime).toBeLessThan(3000)
    })

    test('大量数据渲染性能', async () => {
      // 设置大量渠道数据
      const largeChannelList = Array.from({ length: 100 }, (_, i) => ({
        id: `channel-${i}`,
        platform: 'xianyu',
        platformAccountId: `shop_${i}`,
        displayName: `店铺${i}`,
        status: i % 2 === 0 ? 'connected' : 'disconnected',
        isDeleted: false,
        lastConnectedAt: new Date().toISOString(),
        lastActiveAt: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }))

      await page.route('**/api/v1/channels', async (route) => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            channels: largeChannelList,
            total: 100
          })
        })
      })

      const startTime = Date.now()
      await channelPage.goto()
      
      // 等待列表渲染完成
      await expect(page.locator('[data-testid="channel-list"]')).toBeVisible()
      
      const renderTime = Date.now() - startTime
      
      // 验证渲染时间合理
      expect(renderTime).toBeLessThan(5000)
    })
  })
})
