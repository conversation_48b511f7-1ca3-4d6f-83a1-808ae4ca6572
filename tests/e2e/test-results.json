{"config": {"configFile": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/playwright.config.ts", "rootDir": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 5}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "playwright-report"}], ["json", {"outputFile": "test-results.json"}], ["junit", {"outputFile": "test-results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 5}, "id": "chromium", "name": "chromium", "testDir": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 5}, "id": "firefox", "name": "firefox", "testDir": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 5}, "id": "webkit", "name": "webkit", "testDir": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 5}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 5}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.54.2", "workers": 5, "webServer": null}, "suites": [{"title": "channel_management.spec.ts", "file": "channel_management.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "渠道管理E2E测试", "file": "channel_management.spec.ts", "line": 95, "column": 6, "specs": [], "suites": [{"title": "渠道连接流程", "file": "channel_management.spec.ts", "line": 171, "column": 8, "specs": [{"title": "应该成功连接新渠道", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 7517, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoContainText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('h1')\nExpected string: \u001b[32m\"渠道管理\"\u001b[39m\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('h1')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoContainText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('h1')\nExpected string: \u001b[32m\"渠道管理\"\u001b[39m\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('h1')\u001b[22m\n\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:176:40", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 40, "line": 176}, "snippet": "\u001b[0m \u001b[90m 174 |\u001b[39m\n \u001b[90m 175 |\u001b[39m       \u001b[90m// 验证页面加载\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 176 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h1'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'渠道管理'\u001b[39m)\n \u001b[90m     |\u001b[39m                                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 177 |\u001b[39m\n \u001b[90m 178 |\u001b[39m       \u001b[90m// 点击添加渠道\u001b[39m\n \u001b[90m 179 |\u001b[39m       \u001b[36mawait\u001b[39m channelPage\u001b[33m.\u001b[39mclickAddChannel()\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 40, "line": 176}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoContainText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('h1')\nExpected string: \u001b[32m\"渠道管理\"\u001b[39m\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('h1')\u001b[22m\n\n\n\u001b[0m \u001b[90m 174 |\u001b[39m\n \u001b[90m 175 |\u001b[39m       \u001b[90m// 验证页面加载\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 176 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h1'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'渠道管理'\u001b[39m)\n \u001b[90m     |\u001b[39m                                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 177 |\u001b[39m\n \u001b[90m 178 |\u001b[39m       \u001b[90m// 点击添加渠道\u001b[39m\n \u001b[90m 179 |\u001b[39m       \u001b[36mawait\u001b[39m channelPage\u001b[33m.\u001b[39mclickAddChannel()\u001b[0m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:176:40\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:39:01.741Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该成功连接新渠道-chromium/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该成功连接新渠道-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 40, "line": 176}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-59e3c0b28594f58125bc", "file": "channel_management.spec.ts", "line": 172, "column": 9}, {"title": "应该处理无效Cookie", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "failed", "duration": 12476, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"add-channel-button\"]')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"add-channel-button\"]')\u001b[22m\n\n    at ChannelManagementPage.clickAddChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:33:21)\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:208:25", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 33}, "snippet": "\u001b[0m \u001b[90m 31 |\u001b[39m   \u001b[90m// 添加渠道\u001b[39m\n \u001b[90m 32 |\u001b[39m   \u001b[36masync\u001b[39m clickAddChannel() {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 33 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"add-channel-button\"]'\u001b[39m)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 34 |\u001b[39m   }\n \u001b[90m 35 |\u001b[39m\n \u001b[90m 36 |\u001b[39m   \u001b[90m// 选择平台\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 33}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"add-channel-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 31 |\u001b[39m   \u001b[90m// 添加渠道\u001b[39m\n \u001b[90m 32 |\u001b[39m   \u001b[36masync\u001b[39m clickAddChannel() {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 33 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"add-channel-button\"]'\u001b[39m)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 34 |\u001b[39m   }\n \u001b[90m 35 |\u001b[39m\n \u001b[90m 36 |\u001b[39m   \u001b[90m// 选择平台\u001b[39m\u001b[0m\n\u001b[2m    at ChannelManagementPage.clickAddChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:33:21)\u001b[22m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:208:25\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:39:01.775Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理无效Cookie-chromium/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理无效Cookie-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 33}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-bd8ebdbbe207e930153e", "file": "channel_management.spec.ts", "line": 205, "column": 9}, {"title": "应该处理重复渠道连接", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "failed", "duration": 12612, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"add-channel-button\"]')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"add-channel-button\"]')\u001b[22m\n\n    at ChannelManagementPage.clickAddChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:33:21)\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:236:25", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 33}, "snippet": "\u001b[0m \u001b[90m 31 |\u001b[39m   \u001b[90m// 添加渠道\u001b[39m\n \u001b[90m 32 |\u001b[39m   \u001b[36masync\u001b[39m clickAddChannel() {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 33 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"add-channel-button\"]'\u001b[39m)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 34 |\u001b[39m   }\n \u001b[90m 35 |\u001b[39m\n \u001b[90m 36 |\u001b[39m   \u001b[90m// 选择平台\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 33}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"add-channel-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 31 |\u001b[39m   \u001b[90m// 添加渠道\u001b[39m\n \u001b[90m 32 |\u001b[39m   \u001b[36masync\u001b[39m clickAddChannel() {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 33 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"add-channel-button\"]'\u001b[39m)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 34 |\u001b[39m   }\n \u001b[90m 35 |\u001b[39m\n \u001b[90m 36 |\u001b[39m   \u001b[90m// 选择平台\u001b[39m\u001b[0m\n\u001b[2m    at ChannelManagementPage.clickAddChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:33:21)\u001b[22m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:236:25\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:39:01.774Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理重复渠道连接-chromium/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理重复渠道连接-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 33}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-52cfd0885475ab5b6ac6", "file": "channel_management.spec.ts", "line": 222, "column": 9}, {"title": "应该成功连接新渠道", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 15, "parallelIndex": 1, "status": "failed", "duration": 6788, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoContainText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('h1')\nExpected string: \u001b[32m\"渠道管理\"\u001b[39m\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('h1')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoContainText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('h1')\nExpected string: \u001b[32m\"渠道管理\"\u001b[39m\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('h1')\u001b[22m\n\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:176:40", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 40, "line": 176}, "snippet": "\u001b[0m \u001b[90m 174 |\u001b[39m\n \u001b[90m 175 |\u001b[39m       \u001b[90m// 验证页面加载\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 176 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h1'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'渠道管理'\u001b[39m)\n \u001b[90m     |\u001b[39m                                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 177 |\u001b[39m\n \u001b[90m 178 |\u001b[39m       \u001b[90m// 点击添加渠道\u001b[39m\n \u001b[90m 179 |\u001b[39m       \u001b[36mawait\u001b[39m channelPage\u001b[33m.\u001b[39mclickAddChannel()\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 40, "line": 176}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoContainText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('h1')\nExpected string: \u001b[32m\"渠道管理\"\u001b[39m\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('h1')\u001b[22m\n\n\n\u001b[0m \u001b[90m 174 |\u001b[39m\n \u001b[90m 175 |\u001b[39m       \u001b[90m// 验证页面加载\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 176 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h1'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'渠道管理'\u001b[39m)\n \u001b[90m     |\u001b[39m                                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 177 |\u001b[39m\n \u001b[90m 178 |\u001b[39m       \u001b[90m// 点击添加渠道\u001b[39m\n \u001b[90m 179 |\u001b[39m       \u001b[36mawait\u001b[39m channelPage\u001b[33m.\u001b[39mclickAddChannel()\u001b[0m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:176:40\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:39:29.627Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该成功连接新渠道-firefox/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该成功连接新渠道-firefox/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 40, "line": 176}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-8929b9cfd2183b7e9096", "file": "channel_management.spec.ts", "line": 172, "column": 9}, {"title": "应该处理无效Cookie", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 16, "parallelIndex": 2, "status": "failed", "duration": 11828, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"add-channel-button\"]')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"add-channel-button\"]')\u001b[22m\n\n    at ChannelManagementPage.clickAddChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:33:21)\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:208:25", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 33}, "snippet": "\u001b[0m \u001b[90m 31 |\u001b[39m   \u001b[90m// 添加渠道\u001b[39m\n \u001b[90m 32 |\u001b[39m   \u001b[36masync\u001b[39m clickAddChannel() {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 33 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"add-channel-button\"]'\u001b[39m)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 34 |\u001b[39m   }\n \u001b[90m 35 |\u001b[39m\n \u001b[90m 36 |\u001b[39m   \u001b[90m// 选择平台\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 33}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"add-channel-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 31 |\u001b[39m   \u001b[90m// 添加渠道\u001b[39m\n \u001b[90m 32 |\u001b[39m   \u001b[36masync\u001b[39m clickAddChannel() {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 33 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"add-channel-button\"]'\u001b[39m)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 34 |\u001b[39m   }\n \u001b[90m 35 |\u001b[39m\n \u001b[90m 36 |\u001b[39m   \u001b[90m// 选择平台\u001b[39m\u001b[0m\n\u001b[2m    at ChannelManagementPage.clickAddChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:33:21)\u001b[22m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:208:25\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:39:30.226Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理无效Cookie-firefox/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理无效Cookie-firefox/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 33}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-92170c9f86eb416053f7", "file": "channel_management.spec.ts", "line": 205, "column": 9}, {"title": "应该处理重复渠道连接", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 17, "parallelIndex": 3, "status": "failed", "duration": 11694, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"add-channel-button\"]')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"add-channel-button\"]')\u001b[22m\n\n    at ChannelManagementPage.clickAddChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:33:21)\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:236:25", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 33}, "snippet": "\u001b[0m \u001b[90m 31 |\u001b[39m   \u001b[90m// 添加渠道\u001b[39m\n \u001b[90m 32 |\u001b[39m   \u001b[36masync\u001b[39m clickAddChannel() {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 33 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"add-channel-button\"]'\u001b[39m)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 34 |\u001b[39m   }\n \u001b[90m 35 |\u001b[39m\n \u001b[90m 36 |\u001b[39m   \u001b[90m// 选择平台\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 33}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"add-channel-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 31 |\u001b[39m   \u001b[90m// 添加渠道\u001b[39m\n \u001b[90m 32 |\u001b[39m   \u001b[36masync\u001b[39m clickAddChannel() {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 33 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"add-channel-button\"]'\u001b[39m)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 34 |\u001b[39m   }\n \u001b[90m 35 |\u001b[39m\n \u001b[90m 36 |\u001b[39m   \u001b[90m// 选择平台\u001b[39m\u001b[0m\n\u001b[2m    at ChannelManagementPage.clickAddChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:33:21)\u001b[22m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:236:25\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:39:30.220Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理重复渠道连接-firefox/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理重复渠道连接-firefox/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 33}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-ad3822822464b70b5047", "file": "channel_management.spec.ts", "line": 222, "column": 9}, {"title": "应该成功连接新渠道", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 30, "parallelIndex": 1, "status": "failed", "duration": 6273, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoContainText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('h1')\nExpected string: \u001b[32m\"渠道管理\"\u001b[39m\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('h1')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoContainText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('h1')\nExpected string: \u001b[32m\"渠道管理\"\u001b[39m\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('h1')\u001b[22m\n\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:176:40", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 40, "line": 176}, "snippet": "\u001b[0m \u001b[90m 174 |\u001b[39m\n \u001b[90m 175 |\u001b[39m       \u001b[90m// 验证页面加载\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 176 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h1'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'渠道管理'\u001b[39m)\n \u001b[90m     |\u001b[39m                                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 177 |\u001b[39m\n \u001b[90m 178 |\u001b[39m       \u001b[90m// 点击添加渠道\u001b[39m\n \u001b[90m 179 |\u001b[39m       \u001b[36mawait\u001b[39m channelPage\u001b[33m.\u001b[39mclickAddChannel()\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 40, "line": 176}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoContainText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('h1')\nExpected string: \u001b[32m\"渠道管理\"\u001b[39m\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('h1')\u001b[22m\n\n\n\u001b[0m \u001b[90m 174 |\u001b[39m\n \u001b[90m 175 |\u001b[39m       \u001b[90m// 验证页面加载\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 176 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h1'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'渠道管理'\u001b[39m)\n \u001b[90m     |\u001b[39m                                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 177 |\u001b[39m\n \u001b[90m 178 |\u001b[39m       \u001b[90m// 点击添加渠道\u001b[39m\n \u001b[90m 179 |\u001b[39m       \u001b[36mawait\u001b[39m channelPage\u001b[33m.\u001b[39mclickAddChannel()\u001b[0m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:176:40\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:39:57.783Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该成功连接新渠道-webkit/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该成功连接新渠道-webkit/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 40, "line": 176}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-10757c4d0c61c308e751", "file": "channel_management.spec.ts", "line": 172, "column": 9}, {"title": "应该处理无效Cookie", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 32, "parallelIndex": 2, "status": "failed", "duration": 11049, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"add-channel-button\"]')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"add-channel-button\"]')\u001b[22m\n\n    at ChannelManagementPage.clickAddChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:33:21)\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:208:25", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 33}, "snippet": "\u001b[0m \u001b[90m 31 |\u001b[39m   \u001b[90m// 添加渠道\u001b[39m\n \u001b[90m 32 |\u001b[39m   \u001b[36masync\u001b[39m clickAddChannel() {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 33 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"add-channel-button\"]'\u001b[39m)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 34 |\u001b[39m   }\n \u001b[90m 35 |\u001b[39m\n \u001b[90m 36 |\u001b[39m   \u001b[90m// 选择平台\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 33}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"add-channel-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 31 |\u001b[39m   \u001b[90m// 添加渠道\u001b[39m\n \u001b[90m 32 |\u001b[39m   \u001b[36masync\u001b[39m clickAddChannel() {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 33 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"add-channel-button\"]'\u001b[39m)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 34 |\u001b[39m   }\n \u001b[90m 35 |\u001b[39m\n \u001b[90m 36 |\u001b[39m   \u001b[90m// 选择平台\u001b[39m\u001b[0m\n\u001b[2m    at ChannelManagementPage.clickAddChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:33:21)\u001b[22m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:208:25\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:39:58.503Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理无效Cookie-webkit/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理无效Cookie-webkit/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 33}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-4f3f3642384e5bc6ee89", "file": "channel_management.spec.ts", "line": 205, "column": 9}, {"title": "应该处理重复渠道连接", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 31, "parallelIndex": 0, "status": "failed", "duration": 11087, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"add-channel-button\"]')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"add-channel-button\"]')\u001b[22m\n\n    at ChannelManagementPage.clickAddChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:33:21)\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:236:25", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 33}, "snippet": "\u001b[0m \u001b[90m 31 |\u001b[39m   \u001b[90m// 添加渠道\u001b[39m\n \u001b[90m 32 |\u001b[39m   \u001b[36masync\u001b[39m clickAddChannel() {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 33 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"add-channel-button\"]'\u001b[39m)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 34 |\u001b[39m   }\n \u001b[90m 35 |\u001b[39m\n \u001b[90m 36 |\u001b[39m   \u001b[90m// 选择平台\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 33}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"add-channel-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 31 |\u001b[39m   \u001b[90m// 添加渠道\u001b[39m\n \u001b[90m 32 |\u001b[39m   \u001b[36masync\u001b[39m clickAddChannel() {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 33 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"add-channel-button\"]'\u001b[39m)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 34 |\u001b[39m   }\n \u001b[90m 35 |\u001b[39m\n \u001b[90m 36 |\u001b[39m   \u001b[90m// 选择平台\u001b[39m\u001b[0m\n\u001b[2m    at ChannelManagementPage.clickAddChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:33:21)\u001b[22m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:236:25\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:39:58.345Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理重复渠道连接-webkit/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理重复渠道连接-webkit/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 33}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-40f3bf78628b59b70875", "file": "channel_management.spec.ts", "line": 222, "column": 9}, {"title": "应该成功连接新渠道", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 45, "parallelIndex": 1, "status": "failed", "duration": 6637, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoContainText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('h1')\nExpected string: \u001b[32m\"渠道管理\"\u001b[39m\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('h1')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoContainText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('h1')\nExpected string: \u001b[32m\"渠道管理\"\u001b[39m\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('h1')\u001b[22m\n\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:176:40", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 40, "line": 176}, "snippet": "\u001b[0m \u001b[90m 174 |\u001b[39m\n \u001b[90m 175 |\u001b[39m       \u001b[90m// 验证页面加载\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 176 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h1'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'渠道管理'\u001b[39m)\n \u001b[90m     |\u001b[39m                                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 177 |\u001b[39m\n \u001b[90m 178 |\u001b[39m       \u001b[90m// 点击添加渠道\u001b[39m\n \u001b[90m 179 |\u001b[39m       \u001b[36mawait\u001b[39m channelPage\u001b[33m.\u001b[39mclickAddChannel()\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 40, "line": 176}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoContainText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('h1')\nExpected string: \u001b[32m\"渠道管理\"\u001b[39m\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('h1')\u001b[22m\n\n\n\u001b[0m \u001b[90m 174 |\u001b[39m\n \u001b[90m 175 |\u001b[39m       \u001b[90m// 验证页面加载\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 176 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h1'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'渠道管理'\u001b[39m)\n \u001b[90m     |\u001b[39m                                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 177 |\u001b[39m\n \u001b[90m 178 |\u001b[39m       \u001b[90m// 点击添加渠道\u001b[39m\n \u001b[90m 179 |\u001b[39m       \u001b[36mawait\u001b[39m channelPage\u001b[33m.\u001b[39mclickAddChannel()\u001b[0m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:176:40\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:40:22.278Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该成功连接新渠道-Mobile-Chrome/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该成功连接新渠道-Mobile-Chrome/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 40, "line": 176}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-253832d3e0df80ba8457", "file": "channel_management.spec.ts", "line": 172, "column": 9}, {"title": "应该处理无效Cookie", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 46, "parallelIndex": 2, "status": "failed", "duration": 11418, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"add-channel-button\"]')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"add-channel-button\"]')\u001b[22m\n\n    at ChannelManagementPage.clickAddChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:33:21)\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:208:25", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 33}, "snippet": "\u001b[0m \u001b[90m 31 |\u001b[39m   \u001b[90m// 添加渠道\u001b[39m\n \u001b[90m 32 |\u001b[39m   \u001b[36masync\u001b[39m clickAddChannel() {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 33 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"add-channel-button\"]'\u001b[39m)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 34 |\u001b[39m   }\n \u001b[90m 35 |\u001b[39m\n \u001b[90m 36 |\u001b[39m   \u001b[90m// 选择平台\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 33}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"add-channel-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 31 |\u001b[39m   \u001b[90m// 添加渠道\u001b[39m\n \u001b[90m 32 |\u001b[39m   \u001b[36masync\u001b[39m clickAddChannel() {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 33 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"add-channel-button\"]'\u001b[39m)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 34 |\u001b[39m   }\n \u001b[90m 35 |\u001b[39m\n \u001b[90m 36 |\u001b[39m   \u001b[90m// 选择平台\u001b[39m\u001b[0m\n\u001b[2m    at ChannelManagementPage.clickAddChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:33:21)\u001b[22m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:208:25\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:40:22.833Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理无效Cookie-Mobile-Chrome/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理无效Cookie-Mobile-Chrome/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 33}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-ca0037acebe3b811ba35", "file": "channel_management.spec.ts", "line": 205, "column": 9}, {"title": "应该处理重复渠道连接", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 47, "parallelIndex": 4, "status": "failed", "duration": 11065, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"add-channel-button\"]')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"add-channel-button\"]')\u001b[22m\n\n    at ChannelManagementPage.clickAddChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:33:21)\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:236:25", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 33}, "snippet": "\u001b[0m \u001b[90m 31 |\u001b[39m   \u001b[90m// 添加渠道\u001b[39m\n \u001b[90m 32 |\u001b[39m   \u001b[36masync\u001b[39m clickAddChannel() {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 33 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"add-channel-button\"]'\u001b[39m)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 34 |\u001b[39m   }\n \u001b[90m 35 |\u001b[39m\n \u001b[90m 36 |\u001b[39m   \u001b[90m// 选择平台\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 33}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"add-channel-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 31 |\u001b[39m   \u001b[90m// 添加渠道\u001b[39m\n \u001b[90m 32 |\u001b[39m   \u001b[36masync\u001b[39m clickAddChannel() {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 33 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"add-channel-button\"]'\u001b[39m)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 34 |\u001b[39m   }\n \u001b[90m 35 |\u001b[39m\n \u001b[90m 36 |\u001b[39m   \u001b[90m// 选择平台\u001b[39m\u001b[0m\n\u001b[2m    at ChannelManagementPage.clickAddChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:33:21)\u001b[22m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:236:25\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:40:25.747Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理重复渠道连接-Mobile-Chrome/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理重复渠道连接-Mobile-Chrome/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 33}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-277ded928c58b6d92afc", "file": "channel_management.spec.ts", "line": 222, "column": 9}, {"title": "应该成功连接新渠道", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 60, "parallelIndex": 2, "status": "failed", "duration": 6231, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoContainText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('h1')\nExpected string: \u001b[32m\"渠道管理\"\u001b[39m\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('h1')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoContainText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('h1')\nExpected string: \u001b[32m\"渠道管理\"\u001b[39m\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('h1')\u001b[22m\n\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:176:40", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 40, "line": 176}, "snippet": "\u001b[0m \u001b[90m 174 |\u001b[39m\n \u001b[90m 175 |\u001b[39m       \u001b[90m// 验证页面加载\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 176 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h1'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'渠道管理'\u001b[39m)\n \u001b[90m     |\u001b[39m                                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 177 |\u001b[39m\n \u001b[90m 178 |\u001b[39m       \u001b[90m// 点击添加渠道\u001b[39m\n \u001b[90m 179 |\u001b[39m       \u001b[36mawait\u001b[39m channelPage\u001b[33m.\u001b[39mclickAddChannel()\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 40, "line": 176}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoContainText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('h1')\nExpected string: \u001b[32m\"渠道管理\"\u001b[39m\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('h1')\u001b[22m\n\n\n\u001b[0m \u001b[90m 174 |\u001b[39m\n \u001b[90m 175 |\u001b[39m       \u001b[90m// 验证页面加载\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 176 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h1'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'渠道管理'\u001b[39m)\n \u001b[90m     |\u001b[39m                                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 177 |\u001b[39m\n \u001b[90m 178 |\u001b[39m       \u001b[90m// 点击添加渠道\u001b[39m\n \u001b[90m 179 |\u001b[39m       \u001b[36mawait\u001b[39m channelPage\u001b[33m.\u001b[39mclickAddChannel()\u001b[0m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:176:40\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:40:48.953Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该成功连接新渠道-Mobile-Safari/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该成功连接新渠道-Mobile-Safari/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 40, "line": 176}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-498d60dbb25d2a27fd5d", "file": "channel_management.spec.ts", "line": 172, "column": 9}, {"title": "应该处理无效Cookie", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 61, "parallelIndex": 4, "status": "failed", "duration": 11219, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"add-channel-button\"]')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"add-channel-button\"]')\u001b[22m\n\n    at ChannelManagementPage.clickAddChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:33:21)\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:208:25", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 33}, "snippet": "\u001b[0m \u001b[90m 31 |\u001b[39m   \u001b[90m// 添加渠道\u001b[39m\n \u001b[90m 32 |\u001b[39m   \u001b[36masync\u001b[39m clickAddChannel() {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 33 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"add-channel-button\"]'\u001b[39m)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 34 |\u001b[39m   }\n \u001b[90m 35 |\u001b[39m\n \u001b[90m 36 |\u001b[39m   \u001b[90m// 选择平台\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 33}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"add-channel-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 31 |\u001b[39m   \u001b[90m// 添加渠道\u001b[39m\n \u001b[90m 32 |\u001b[39m   \u001b[36masync\u001b[39m clickAddChannel() {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 33 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"add-channel-button\"]'\u001b[39m)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 34 |\u001b[39m   }\n \u001b[90m 35 |\u001b[39m\n \u001b[90m 36 |\u001b[39m   \u001b[90m// 选择平台\u001b[39m\u001b[0m\n\u001b[2m    at ChannelManagementPage.clickAddChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:33:21)\u001b[22m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:208:25\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:40:49.036Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理无效Cookie-Mobile-Safari/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理无效Cookie-Mobile-Safari/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 33}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-e15314abf661913426e2", "file": "channel_management.spec.ts", "line": 205, "column": 9}, {"title": "应该处理重复渠道连接", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 62, "parallelIndex": 0, "status": "failed", "duration": 10924, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"add-channel-button\"]')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"add-channel-button\"]')\u001b[22m\n\n    at ChannelManagementPage.clickAddChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:33:21)\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:236:25", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 33}, "snippet": "\u001b[0m \u001b[90m 31 |\u001b[39m   \u001b[90m// 添加渠道\u001b[39m\n \u001b[90m 32 |\u001b[39m   \u001b[36masync\u001b[39m clickAddChannel() {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 33 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"add-channel-button\"]'\u001b[39m)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 34 |\u001b[39m   }\n \u001b[90m 35 |\u001b[39m\n \u001b[90m 36 |\u001b[39m   \u001b[90m// 选择平台\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 33}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"add-channel-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 31 |\u001b[39m   \u001b[90m// 添加渠道\u001b[39m\n \u001b[90m 32 |\u001b[39m   \u001b[36masync\u001b[39m clickAddChannel() {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 33 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"add-channel-button\"]'\u001b[39m)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 34 |\u001b[39m   }\n \u001b[90m 35 |\u001b[39m\n \u001b[90m 36 |\u001b[39m   \u001b[90m// 选择平台\u001b[39m\u001b[0m\n\u001b[2m    at ChannelManagementPage.clickAddChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:33:21)\u001b[22m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:236:25\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:40:52.799Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理重复渠道连接-Mobile-Safari/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道连接流程-应该处理重复渠道连接-Mobile-Safari/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 33}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-8123fd67e507d0b6a0f4", "file": "channel_management.spec.ts", "line": 222, "column": 9}]}, {"title": "渠道管理操作", "file": "channel_management.spec.ts", "line": 247, "column": 8, "specs": [{"title": "应该显示渠道列表", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "failed", "duration": 7482, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveCount\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('[data-testid=\"channel-list\"] [data-testid^=\"channel-\"]')\nExpected: \u001b[32m2\u001b[39m\nReceived: \u001b[31m0\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveCount\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"channel-list\"] [data-testid^=\"channel-\"]')\u001b[22m\n\u001b[2m    9 × locator resolved to 0 elements\u001b[22m\n\u001b[2m      - unexpected value \"0\"\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveCount\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('[data-testid=\"channel-list\"] [data-testid^=\"channel-\"]')\nExpected: \u001b[32m2\u001b[39m\nReceived: \u001b[31m0\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveCount\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"channel-list\"] [data-testid^=\"channel-\"]')\u001b[22m\n\u001b[2m    9 × locator resolved to 0 elements\u001b[22m\n\u001b[2m      - unexpected value \"0\"\u001b[22m\n\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:292:30", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 30, "line": 292}, "snippet": "\u001b[0m \u001b[90m 290 |\u001b[39m       \u001b[90m// 验证渠道列表显示\u001b[39m\n \u001b[90m 291 |\u001b[39m       \u001b[36mconst\u001b[39m channels \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m channelPage\u001b[33m.\u001b[39mgetChannelList()\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 292 |\u001b[39m       \u001b[36mawait\u001b[39m expect(channels)\u001b[33m.\u001b[39mtoHaveCount(\u001b[35m2\u001b[39m)\n \u001b[90m     |\u001b[39m                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 293 |\u001b[39m\n \u001b[90m 294 |\u001b[39m       \u001b[90m// 验证渠道信息\u001b[39m\n \u001b[90m 295 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"channel-channel-1\"]'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'主店铺'\u001b[39m)\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 30, "line": 292}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveCount\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('[data-testid=\"channel-list\"] [data-testid^=\"channel-\"]')\nExpected: \u001b[32m2\u001b[39m\nReceived: \u001b[31m0\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveCount\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"channel-list\"] [data-testid^=\"channel-\"]')\u001b[22m\n\u001b[2m    9 × locator resolved to 0 elements\u001b[22m\n\u001b[2m      - unexpected value \"0\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 290 |\u001b[39m       \u001b[90m// 验证渠道列表显示\u001b[39m\n \u001b[90m 291 |\u001b[39m       \u001b[36mconst\u001b[39m channels \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m channelPage\u001b[33m.\u001b[39mgetChannelList()\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 292 |\u001b[39m       \u001b[36mawait\u001b[39m expect(channels)\u001b[33m.\u001b[39mtoHaveCount(\u001b[35m2\u001b[39m)\n \u001b[90m     |\u001b[39m                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 293 |\u001b[39m\n \u001b[90m 294 |\u001b[39m       \u001b[90m// 验证渠道信息\u001b[39m\n \u001b[90m 295 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"channel-channel-1\"]'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'主店铺'\u001b[39m)\u001b[0m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:292:30\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:39:01.770Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该显示渠道列表-chromium/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该显示渠道列表-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 30, "line": 292}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-176439d23833ed8361e1", "file": "channel_management.spec.ts", "line": 287, "column": 9}, {"title": "应该支持搜索功能", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "failed", "duration": 12573, "error": {"message": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"search-input\"]')\u001b[22m\n", "stack": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"search-input\"]')\u001b[22m\n\n    at ChannelManagementPage.searchChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:63:21)\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:307:25", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 63}, "snippet": "\u001b[0m \u001b[90m 61 |\u001b[39m   \u001b[90m// 搜索渠道\u001b[39m\n \u001b[90m 62 |\u001b[39m   \u001b[36masync\u001b[39m searchChannel(query\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 63 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"search-input\"]'\u001b[39m\u001b[33m,\u001b[39m query)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 64 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mkeyboard\u001b[33m.\u001b[39mpress(\u001b[32m'Enter'\u001b[39m)\n \u001b[90m 65 |\u001b[39m   }\n \u001b[90m 66 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 63}, "message": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"search-input\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 61 |\u001b[39m   \u001b[90m// 搜索渠道\u001b[39m\n \u001b[90m 62 |\u001b[39m   \u001b[36masync\u001b[39m searchChannel(query\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 63 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"search-input\"]'\u001b[39m\u001b[33m,\u001b[39m query)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 64 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mkeyboard\u001b[33m.\u001b[39mpress(\u001b[32m'Enter'\u001b[39m)\n \u001b[90m 65 |\u001b[39m   }\n \u001b[90m 66 |\u001b[39m\u001b[0m\n\u001b[2m    at ChannelManagementPage.searchChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:63:21)\u001b[22m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:307:25\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:39:01.754Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持搜索功能-chromium/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持搜索功能-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 63}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-e9fa30465ffe34cd32f5", "file": "channel_management.spec.ts", "line": 303, "column": 9}, {"title": "应该支持状态筛选", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 3, "status": "failed", "duration": 11304, "error": {"message": "TimeoutError: page.selectOption: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"status-filter\"]')\u001b[22m\n", "stack": "TimeoutError: page.selectOption: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"status-filter\"]')\u001b[22m\n\n    at ChannelManagementPage.filterByStatus (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:69:21)\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:318:25", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 69}, "snippet": "\u001b[0m \u001b[90m 67 |\u001b[39m   \u001b[90m// 筛选渠道\u001b[39m\n \u001b[90m 68 |\u001b[39m   \u001b[36masync\u001b[39m filterByStatus(status\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 69 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mselectOption(\u001b[32m'[data-testid=\"status-filter\"]'\u001b[39m\u001b[33m,\u001b[39m status)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 70 |\u001b[39m   }\n \u001b[90m 71 |\u001b[39m\n \u001b[90m 72 |\u001b[39m   \u001b[90m// 删除渠道\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 69}, "message": "TimeoutError: page.selectOption: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"status-filter\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 67 |\u001b[39m   \u001b[90m// 筛选渠道\u001b[39m\n \u001b[90m 68 |\u001b[39m   \u001b[36masync\u001b[39m filterByStatus(status\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 69 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mselectOption(\u001b[32m'[data-testid=\"status-filter\"]'\u001b[39m\u001b[33m,\u001b[39m status)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 70 |\u001b[39m   }\n \u001b[90m 71 |\u001b[39m\n \u001b[90m 72 |\u001b[39m   \u001b[90m// 删除渠道\u001b[39m\u001b[0m\n\u001b[2m    at ChannelManagementPage.filterByStatus (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:69:21)\u001b[22m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:318:25\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:39:10.904Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持状态筛选-chromium/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持状态筛选-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 69}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-ff398584f8adf49dbcf6", "file": "channel_management.spec.ts", "line": 314, "column": 9}, {"title": "应该支持删除和恢复渠道", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 6, "parallelIndex": 0, "status": "failed", "duration": 11490, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"channel-channel-1\"] [data-testid=\"delete-button\"]')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"channel-channel-1\"] [data-testid=\"delete-button\"]')\u001b[22m\n\n    at ChannelManagementPage.deleteChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:74:21)\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:348:25", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 74}, "snippet": "\u001b[0m \u001b[90m 72 |\u001b[39m   \u001b[90m// 删除渠道\u001b[39m\n \u001b[90m 73 |\u001b[39m   \u001b[36masync\u001b[39m deleteChannel(channelId\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 74 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m`[data-testid=\"channel-${channelId}\"] [data-testid=\"delete-button\"]`\u001b[39m)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 75 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"confirm-delete\"]'\u001b[39m)\n \u001b[90m 76 |\u001b[39m   }\n \u001b[90m 77 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 74}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"channel-channel-1\"] [data-testid=\"delete-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 72 |\u001b[39m   \u001b[90m// 删除渠道\u001b[39m\n \u001b[90m 73 |\u001b[39m   \u001b[36masync\u001b[39m deleteChannel(channelId\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 74 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m`[data-testid=\"channel-${channelId}\"] [data-testid=\"delete-button\"]`\u001b[39m)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 75 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"confirm-delete\"]'\u001b[39m)\n \u001b[90m 76 |\u001b[39m   }\n \u001b[90m 77 |\u001b[39m\u001b[0m\n\u001b[2m    at ChannelManagementPage.deleteChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:74:21)\u001b[22m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:348:25\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:39:10.904Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持删除和恢复渠道-chromium/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持删除和恢复渠道-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 74}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-8fb6edf57d7d8775bca2", "file": "channel_management.spec.ts", "line": 325, "column": 9}, {"title": "应该显示渠道列表", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 18, "parallelIndex": 0, "status": "failed", "duration": 6892, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveCount\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('[data-testid=\"channel-list\"] [data-testid^=\"channel-\"]')\nExpected: \u001b[32m2\u001b[39m\nReceived: \u001b[31m0\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveCount\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"channel-list\"] [data-testid^=\"channel-\"]')\u001b[22m\n\u001b[2m    9 × locator resolved to 0 elements\u001b[22m\n\u001b[2m      - unexpected value \"0\"\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveCount\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('[data-testid=\"channel-list\"] [data-testid^=\"channel-\"]')\nExpected: \u001b[32m2\u001b[39m\nReceived: \u001b[31m0\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveCount\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"channel-list\"] [data-testid^=\"channel-\"]')\u001b[22m\n\u001b[2m    9 × locator resolved to 0 elements\u001b[22m\n\u001b[2m      - unexpected value \"0\"\u001b[22m\n\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:292:30", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 30, "line": 292}, "snippet": "\u001b[0m \u001b[90m 290 |\u001b[39m       \u001b[90m// 验证渠道列表显示\u001b[39m\n \u001b[90m 291 |\u001b[39m       \u001b[36mconst\u001b[39m channels \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m channelPage\u001b[33m.\u001b[39mgetChannelList()\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 292 |\u001b[39m       \u001b[36mawait\u001b[39m expect(channels)\u001b[33m.\u001b[39mtoHaveCount(\u001b[35m2\u001b[39m)\n \u001b[90m     |\u001b[39m                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 293 |\u001b[39m\n \u001b[90m 294 |\u001b[39m       \u001b[90m// 验证渠道信息\u001b[39m\n \u001b[90m 295 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"channel-channel-1\"]'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'主店铺'\u001b[39m)\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 30, "line": 292}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveCount\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('[data-testid=\"channel-list\"] [data-testid^=\"channel-\"]')\nExpected: \u001b[32m2\u001b[39m\nReceived: \u001b[31m0\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveCount\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"channel-list\"] [data-testid^=\"channel-\"]')\u001b[22m\n\u001b[2m    9 × locator resolved to 0 elements\u001b[22m\n\u001b[2m      - unexpected value \"0\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 290 |\u001b[39m       \u001b[90m// 验证渠道列表显示\u001b[39m\n \u001b[90m 291 |\u001b[39m       \u001b[36mconst\u001b[39m channels \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m channelPage\u001b[33m.\u001b[39mgetChannelList()\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 292 |\u001b[39m       \u001b[36mawait\u001b[39m expect(channels)\u001b[33m.\u001b[39mtoHaveCount(\u001b[35m2\u001b[39m)\n \u001b[90m     |\u001b[39m                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 293 |\u001b[39m\n \u001b[90m 294 |\u001b[39m       \u001b[90m// 验证渠道信息\u001b[39m\n \u001b[90m 295 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"channel-channel-1\"]'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'主店铺'\u001b[39m)\u001b[0m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:292:30\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:39:30.283Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该显示渠道列表-firefox/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该显示渠道列表-firefox/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 30, "line": 292}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-19a2f10ce6d1b7f9b7c6", "file": "channel_management.spec.ts", "line": 287, "column": 9}, {"title": "应该支持搜索功能", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 19, "parallelIndex": 4, "status": "failed", "duration": 11663, "error": {"message": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"search-input\"]')\u001b[22m\n", "stack": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"search-input\"]')\u001b[22m\n\n    at ChannelManagementPage.searchChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:63:21)\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:307:25", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 63}, "snippet": "\u001b[0m \u001b[90m 61 |\u001b[39m   \u001b[90m// 搜索渠道\u001b[39m\n \u001b[90m 62 |\u001b[39m   \u001b[36masync\u001b[39m searchChannel(query\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 63 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"search-input\"]'\u001b[39m\u001b[33m,\u001b[39m query)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 64 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mkeyboard\u001b[33m.\u001b[39mpress(\u001b[32m'Enter'\u001b[39m)\n \u001b[90m 65 |\u001b[39m   }\n \u001b[90m 66 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 63}, "message": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"search-input\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 61 |\u001b[39m   \u001b[90m// 搜索渠道\u001b[39m\n \u001b[90m 62 |\u001b[39m   \u001b[36masync\u001b[39m searchChannel(query\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 63 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"search-input\"]'\u001b[39m\u001b[33m,\u001b[39m query)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 64 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mkeyboard\u001b[33m.\u001b[39mpress(\u001b[32m'Enter'\u001b[39m)\n \u001b[90m 65 |\u001b[39m   }\n \u001b[90m 66 |\u001b[39m\u001b[0m\n\u001b[2m    at ChannelManagementPage.searchChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:63:21)\u001b[22m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:307:25\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:39:34.723Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持搜索功能-firefox/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持搜索功能-firefox/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 63}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-f6d3fce1fac73a4a9350", "file": "channel_management.spec.ts", "line": 303, "column": 9}, {"title": "应该支持状态筛选", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 20, "parallelIndex": 1, "status": "failed", "duration": 11448, "error": {"message": "TimeoutError: page.selectOption: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"status-filter\"]')\u001b[22m\n", "stack": "TimeoutError: page.selectOption: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"status-filter\"]')\u001b[22m\n\n    at ChannelManagementPage.filterByStatus (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:69:21)\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:318:25", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 69}, "snippet": "\u001b[0m \u001b[90m 67 |\u001b[39m   \u001b[90m// 筛选渠道\u001b[39m\n \u001b[90m 68 |\u001b[39m   \u001b[36masync\u001b[39m filterByStatus(status\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 69 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mselectOption(\u001b[32m'[data-testid=\"status-filter\"]'\u001b[39m\u001b[33m,\u001b[39m status)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 70 |\u001b[39m   }\n \u001b[90m 71 |\u001b[39m\n \u001b[90m 72 |\u001b[39m   \u001b[90m// 删除渠道\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 69}, "message": "TimeoutError: page.selectOption: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"status-filter\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 67 |\u001b[39m   \u001b[90m// 筛选渠道\u001b[39m\n \u001b[90m 68 |\u001b[39m   \u001b[36masync\u001b[39m filterByStatus(status\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 69 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mselectOption(\u001b[32m'[data-testid=\"status-filter\"]'\u001b[39m\u001b[33m,\u001b[39m status)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 70 |\u001b[39m   }\n \u001b[90m 71 |\u001b[39m\n \u001b[90m 72 |\u001b[39m   \u001b[90m// 删除渠道\u001b[39m\u001b[0m\n\u001b[2m    at ChannelManagementPage.filterByStatus (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:69:21)\u001b[22m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:318:25\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:39:37.744Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持状态筛选-firefox/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持状态筛选-firefox/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 69}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-6484707ddf71d7c953c9", "file": "channel_management.spec.ts", "line": 314, "column": 9}, {"title": "应该支持删除和恢复渠道", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 21, "parallelIndex": 0, "status": "failed", "duration": 11470, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"channel-channel-1\"] [data-testid=\"delete-button\"]')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"channel-channel-1\"] [data-testid=\"delete-button\"]')\u001b[22m\n\n    at ChannelManagementPage.deleteChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:74:21)\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:348:25", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 74}, "snippet": "\u001b[0m \u001b[90m 72 |\u001b[39m   \u001b[90m// 删除渠道\u001b[39m\n \u001b[90m 73 |\u001b[39m   \u001b[36masync\u001b[39m deleteChannel(channelId\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 74 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m`[data-testid=\"channel-${channelId}\"] [data-testid=\"delete-button\"]`\u001b[39m)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 75 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"confirm-delete\"]'\u001b[39m)\n \u001b[90m 76 |\u001b[39m   }\n \u001b[90m 77 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 74}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"channel-channel-1\"] [data-testid=\"delete-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 72 |\u001b[39m   \u001b[90m// 删除渠道\u001b[39m\n \u001b[90m 73 |\u001b[39m   \u001b[36masync\u001b[39m deleteChannel(channelId\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 74 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m`[data-testid=\"channel-${channelId}\"] [data-testid=\"delete-button\"]`\u001b[39m)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 75 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"confirm-delete\"]'\u001b[39m)\n \u001b[90m 76 |\u001b[39m   }\n \u001b[90m 77 |\u001b[39m\u001b[0m\n\u001b[2m    at ChannelManagementPage.deleteChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:74:21)\u001b[22m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:348:25\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:39:38.308Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持删除和恢复渠道-firefox/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持删除和恢复渠道-firefox/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 74}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-bb478c5e6b6f82794ffd", "file": "channel_management.spec.ts", "line": 325, "column": 9}, {"title": "应该显示渠道列表", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 33, "parallelIndex": 4, "status": "failed", "duration": 5919, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveCount\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('[data-testid=\"channel-list\"] [data-testid^=\"channel-\"]')\nExpected: \u001b[32m2\u001b[39m\nReceived: \u001b[31m0\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveCount\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"channel-list\"] [data-testid^=\"channel-\"]')\u001b[22m\n\u001b[2m    9 × locator resolved to 0 elements\u001b[22m\n\u001b[2m      - unexpected value \"0\"\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveCount\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('[data-testid=\"channel-list\"] [data-testid^=\"channel-\"]')\nExpected: \u001b[32m2\u001b[39m\nReceived: \u001b[31m0\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveCount\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"channel-list\"] [data-testid^=\"channel-\"]')\u001b[22m\n\u001b[2m    9 × locator resolved to 0 elements\u001b[22m\n\u001b[2m      - unexpected value \"0\"\u001b[22m\n\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:292:30", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 30, "line": 292}, "snippet": "\u001b[0m \u001b[90m 290 |\u001b[39m       \u001b[90m// 验证渠道列表显示\u001b[39m\n \u001b[90m 291 |\u001b[39m       \u001b[36mconst\u001b[39m channels \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m channelPage\u001b[33m.\u001b[39mgetChannelList()\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 292 |\u001b[39m       \u001b[36mawait\u001b[39m expect(channels)\u001b[33m.\u001b[39mtoHaveCount(\u001b[35m2\u001b[39m)\n \u001b[90m     |\u001b[39m                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 293 |\u001b[39m\n \u001b[90m 294 |\u001b[39m       \u001b[90m// 验证渠道信息\u001b[39m\n \u001b[90m 295 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"channel-channel-1\"]'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'主店铺'\u001b[39m)\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 30, "line": 292}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveCount\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('[data-testid=\"channel-list\"] [data-testid^=\"channel-\"]')\nExpected: \u001b[32m2\u001b[39m\nReceived: \u001b[31m0\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveCount\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"channel-list\"] [data-testid^=\"channel-\"]')\u001b[22m\n\u001b[2m    9 × locator resolved to 0 elements\u001b[22m\n\u001b[2m      - unexpected value \"0\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 290 |\u001b[39m       \u001b[90m// 验证渠道列表显示\u001b[39m\n \u001b[90m 291 |\u001b[39m       \u001b[36mconst\u001b[39m channels \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m channelPage\u001b[33m.\u001b[39mgetChannelList()\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 292 |\u001b[39m       \u001b[36mawait\u001b[39m expect(channels)\u001b[33m.\u001b[39mtoHaveCount(\u001b[35m2\u001b[39m)\n \u001b[90m     |\u001b[39m                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 293 |\u001b[39m\n \u001b[90m 294 |\u001b[39m       \u001b[90m// 验证渠道信息\u001b[39m\n \u001b[90m 295 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"channel-channel-1\"]'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'主店铺'\u001b[39m)\u001b[0m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:292:30\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:40:01.884Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该显示渠道列表-webkit/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该显示渠道列表-webkit/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 30, "line": 292}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-e38b49c34296f2f34631", "file": "channel_management.spec.ts", "line": 287, "column": 9}, {"title": "应该支持搜索功能", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 34, "parallelIndex": 3, "status": "failed", "duration": 10949, "error": {"message": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"search-input\"]')\u001b[22m\n", "stack": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"search-input\"]')\u001b[22m\n\n    at ChannelManagementPage.searchChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:63:21)\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:307:25", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 63}, "snippet": "\u001b[0m \u001b[90m 61 |\u001b[39m   \u001b[90m// 搜索渠道\u001b[39m\n \u001b[90m 62 |\u001b[39m   \u001b[36masync\u001b[39m searchChannel(query\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 63 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"search-input\"]'\u001b[39m\u001b[33m,\u001b[39m query)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 64 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mkeyboard\u001b[33m.\u001b[39mpress(\u001b[32m'Enter'\u001b[39m)\n \u001b[90m 65 |\u001b[39m   }\n \u001b[90m 66 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 63}, "message": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"search-input\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 61 |\u001b[39m   \u001b[90m// 搜索渠道\u001b[39m\n \u001b[90m 62 |\u001b[39m   \u001b[36masync\u001b[39m searchChannel(query\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 63 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"search-input\"]'\u001b[39m\u001b[33m,\u001b[39m query)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 64 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mkeyboard\u001b[33m.\u001b[39mpress(\u001b[32m'Enter'\u001b[39m)\n \u001b[90m 65 |\u001b[39m   }\n \u001b[90m 66 |\u001b[39m\u001b[0m\n\u001b[2m    at ChannelManagementPage.searchChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:63:21)\u001b[22m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:307:25\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:40:03.873Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持搜索功能-webkit/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持搜索功能-webkit/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 63}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-ad14f1889c30f82160cb", "file": "channel_management.spec.ts", "line": 303, "column": 9}, {"title": "应该支持状态筛选", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 35, "parallelIndex": 1, "status": "failed", "duration": 10902, "error": {"message": "TimeoutError: page.selectOption: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"status-filter\"]')\u001b[22m\n", "stack": "TimeoutError: page.selectOption: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"status-filter\"]')\u001b[22m\n\n    at ChannelManagementPage.filterByStatus (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:69:21)\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:318:25", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 69}, "snippet": "\u001b[0m \u001b[90m 67 |\u001b[39m   \u001b[90m// 筛选渠道\u001b[39m\n \u001b[90m 68 |\u001b[39m   \u001b[36masync\u001b[39m filterByStatus(status\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 69 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mselectOption(\u001b[32m'[data-testid=\"status-filter\"]'\u001b[39m\u001b[33m,\u001b[39m status)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 70 |\u001b[39m   }\n \u001b[90m 71 |\u001b[39m\n \u001b[90m 72 |\u001b[39m   \u001b[90m// 删除渠道\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 69}, "message": "TimeoutError: page.selectOption: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"status-filter\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 67 |\u001b[39m   \u001b[90m// 筛选渠道\u001b[39m\n \u001b[90m 68 |\u001b[39m   \u001b[36masync\u001b[39m filterByStatus(status\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 69 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mselectOption(\u001b[32m'[data-testid=\"status-filter\"]'\u001b[39m\u001b[33m,\u001b[39m status)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 70 |\u001b[39m   }\n \u001b[90m 71 |\u001b[39m\n \u001b[90m 72 |\u001b[39m   \u001b[90m// 删除渠道\u001b[39m\u001b[0m\n\u001b[2m    at ChannelManagementPage.filterByStatus (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:69:21)\u001b[22m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:318:25\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:40:04.584Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持状态筛选-webkit/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持状态筛选-webkit/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 69}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-6177d09424cf40091632", "file": "channel_management.spec.ts", "line": 314, "column": 9}, {"title": "应该支持删除和恢复渠道", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 36, "parallelIndex": 4, "status": "failed", "duration": 10912, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"channel-channel-1\"] [data-testid=\"delete-button\"]')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"channel-channel-1\"] [data-testid=\"delete-button\"]')\u001b[22m\n\n    at ChannelManagementPage.deleteChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:74:21)\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:348:25", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 74}, "snippet": "\u001b[0m \u001b[90m 72 |\u001b[39m   \u001b[90m// 删除渠道\u001b[39m\n \u001b[90m 73 |\u001b[39m   \u001b[36masync\u001b[39m deleteChannel(channelId\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 74 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m`[data-testid=\"channel-${channelId}\"] [data-testid=\"delete-button\"]`\u001b[39m)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 75 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"confirm-delete\"]'\u001b[39m)\n \u001b[90m 76 |\u001b[39m   }\n \u001b[90m 77 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 74}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"channel-channel-1\"] [data-testid=\"delete-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 72 |\u001b[39m   \u001b[90m// 删除渠道\u001b[39m\n \u001b[90m 73 |\u001b[39m   \u001b[36masync\u001b[39m deleteChannel(channelId\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 74 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m`[data-testid=\"channel-${channelId}\"] [data-testid=\"delete-button\"]`\u001b[39m)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 75 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"confirm-delete\"]'\u001b[39m)\n \u001b[90m 76 |\u001b[39m   }\n \u001b[90m 77 |\u001b[39m\u001b[0m\n\u001b[2m    at ChannelManagementPage.deleteChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:74:21)\u001b[22m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:348:25\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:40:08.168Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持删除和恢复渠道-webkit/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持删除和恢复渠道-webkit/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 74}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-de3bb321761c4a8b7f49", "file": "channel_management.spec.ts", "line": 325, "column": 9}, {"title": "应该显示渠道列表", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 48, "parallelIndex": 0, "status": "failed", "duration": 6043, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveCount\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('[data-testid=\"channel-list\"] [data-testid^=\"channel-\"]')\nExpected: \u001b[32m2\u001b[39m\nReceived: \u001b[31m0\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveCount\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"channel-list\"] [data-testid^=\"channel-\"]')\u001b[22m\n\u001b[2m    9 × locator resolved to 0 elements\u001b[22m\n\u001b[2m      - unexpected value \"0\"\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveCount\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('[data-testid=\"channel-list\"] [data-testid^=\"channel-\"]')\nExpected: \u001b[32m2\u001b[39m\nReceived: \u001b[31m0\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveCount\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"channel-list\"] [data-testid^=\"channel-\"]')\u001b[22m\n\u001b[2m    9 × locator resolved to 0 elements\u001b[22m\n\u001b[2m      - unexpected value \"0\"\u001b[22m\n\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:292:30", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 30, "line": 292}, "snippet": "\u001b[0m \u001b[90m 290 |\u001b[39m       \u001b[90m// 验证渠道列表显示\u001b[39m\n \u001b[90m 291 |\u001b[39m       \u001b[36mconst\u001b[39m channels \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m channelPage\u001b[33m.\u001b[39mgetChannelList()\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 292 |\u001b[39m       \u001b[36mawait\u001b[39m expect(channels)\u001b[33m.\u001b[39mtoHaveCount(\u001b[35m2\u001b[39m)\n \u001b[90m     |\u001b[39m                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 293 |\u001b[39m\n \u001b[90m 294 |\u001b[39m       \u001b[90m// 验证渠道信息\u001b[39m\n \u001b[90m 295 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"channel-channel-1\"]'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'主店铺'\u001b[39m)\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 30, "line": 292}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveCount\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('[data-testid=\"channel-list\"] [data-testid^=\"channel-\"]')\nExpected: \u001b[32m2\u001b[39m\nReceived: \u001b[31m0\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveCount\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"channel-list\"] [data-testid^=\"channel-\"]')\u001b[22m\n\u001b[2m    9 × locator resolved to 0 elements\u001b[22m\n\u001b[2m      - unexpected value \"0\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 290 |\u001b[39m       \u001b[90m// 验证渠道列表显示\u001b[39m\n \u001b[90m 291 |\u001b[39m       \u001b[36mconst\u001b[39m channels \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m channelPage\u001b[33m.\u001b[39mgetChannelList()\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 292 |\u001b[39m       \u001b[36mawait\u001b[39m expect(channels)\u001b[33m.\u001b[39mtoHaveCount(\u001b[35m2\u001b[39m)\n \u001b[90m     |\u001b[39m                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 293 |\u001b[39m\n \u001b[90m 294 |\u001b[39m       \u001b[90m// 验证渠道信息\u001b[39m\n \u001b[90m 295 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"channel-channel-1\"]'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'主店铺'\u001b[39m)\u001b[0m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:292:30\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:40:27.562Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该显示渠道列表-Mobile-Chrome/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该显示渠道列表-Mobile-Chrome/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 30, "line": 292}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-57bd7c534fb9082b7c43", "file": "channel_management.spec.ts", "line": 287, "column": 9}, {"title": "应该支持搜索功能", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 49, "parallelIndex": 3, "status": "failed", "duration": 11212, "error": {"message": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"search-input\"]')\u001b[22m\n", "stack": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"search-input\"]')\u001b[22m\n\n    at ChannelManagementPage.searchChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:63:21)\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:307:25", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 63}, "snippet": "\u001b[0m \u001b[90m 61 |\u001b[39m   \u001b[90m// 搜索渠道\u001b[39m\n \u001b[90m 62 |\u001b[39m   \u001b[36masync\u001b[39m searchChannel(query\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 63 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"search-input\"]'\u001b[39m\u001b[33m,\u001b[39m query)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 64 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mkeyboard\u001b[33m.\u001b[39mpress(\u001b[32m'Enter'\u001b[39m)\n \u001b[90m 65 |\u001b[39m   }\n \u001b[90m 66 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 63}, "message": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"search-input\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 61 |\u001b[39m   \u001b[90m// 搜索渠道\u001b[39m\n \u001b[90m 62 |\u001b[39m   \u001b[36masync\u001b[39m searchChannel(query\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 63 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"search-input\"]'\u001b[39m\u001b[33m,\u001b[39m query)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 64 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mkeyboard\u001b[33m.\u001b[39mpress(\u001b[32m'Enter'\u001b[39m)\n \u001b[90m 65 |\u001b[39m   }\n \u001b[90m 66 |\u001b[39m\u001b[0m\n\u001b[2m    at ChannelManagementPage.searchChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:63:21)\u001b[22m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:307:25\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:40:28.255Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持搜索功能-Mobile-Chrome/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持搜索功能-Mobile-Chrome/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 63}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-07de392471e38ad4378e", "file": "channel_management.spec.ts", "line": 303, "column": 9}, {"title": "应该支持状态筛选", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 50, "parallelIndex": 1, "status": "failed", "duration": 11138, "error": {"message": "TimeoutError: page.selectOption: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"status-filter\"]')\u001b[22m\n", "stack": "TimeoutError: page.selectOption: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"status-filter\"]')\u001b[22m\n\n    at ChannelManagementPage.filterByStatus (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:69:21)\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:318:25", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 69}, "snippet": "\u001b[0m \u001b[90m 67 |\u001b[39m   \u001b[90m// 筛选渠道\u001b[39m\n \u001b[90m 68 |\u001b[39m   \u001b[36masync\u001b[39m filterByStatus(status\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 69 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mselectOption(\u001b[32m'[data-testid=\"status-filter\"]'\u001b[39m\u001b[33m,\u001b[39m status)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 70 |\u001b[39m   }\n \u001b[90m 71 |\u001b[39m\n \u001b[90m 72 |\u001b[39m   \u001b[90m// 删除渠道\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 69}, "message": "TimeoutError: page.selectOption: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"status-filter\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 67 |\u001b[39m   \u001b[90m// 筛选渠道\u001b[39m\n \u001b[90m 68 |\u001b[39m   \u001b[36masync\u001b[39m filterByStatus(status\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 69 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mselectOption(\u001b[32m'[data-testid=\"status-filter\"]'\u001b[39m\u001b[33m,\u001b[39m status)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 70 |\u001b[39m   }\n \u001b[90m 71 |\u001b[39m\n \u001b[90m 72 |\u001b[39m   \u001b[90m// 删除渠道\u001b[39m\u001b[0m\n\u001b[2m    at ChannelManagementPage.filterByStatus (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:69:21)\u001b[22m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:318:25\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:40:30.026Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持状态筛选-Mobile-Chrome/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持状态筛选-Mobile-Chrome/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 69}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-e8374ed294bbdf5a85c0", "file": "channel_management.spec.ts", "line": 314, "column": 9}, {"title": "应该支持删除和恢复渠道", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 51, "parallelIndex": 0, "status": "failed", "duration": 11125, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"channel-channel-1\"] [data-testid=\"delete-button\"]')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"channel-channel-1\"] [data-testid=\"delete-button\"]')\u001b[22m\n\n    at ChannelManagementPage.deleteChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:74:21)\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:348:25", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 74}, "snippet": "\u001b[0m \u001b[90m 72 |\u001b[39m   \u001b[90m// 删除渠道\u001b[39m\n \u001b[90m 73 |\u001b[39m   \u001b[36masync\u001b[39m deleteChannel(channelId\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 74 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m`[data-testid=\"channel-${channelId}\"] [data-testid=\"delete-button\"]`\u001b[39m)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 75 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"confirm-delete\"]'\u001b[39m)\n \u001b[90m 76 |\u001b[39m   }\n \u001b[90m 77 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 74}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"channel-channel-1\"] [data-testid=\"delete-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 72 |\u001b[39m   \u001b[90m// 删除渠道\u001b[39m\n \u001b[90m 73 |\u001b[39m   \u001b[36masync\u001b[39m deleteChannel(channelId\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 74 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m`[data-testid=\"channel-${channelId}\"] [data-testid=\"delete-button\"]`\u001b[39m)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 75 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"confirm-delete\"]'\u001b[39m)\n \u001b[90m 76 |\u001b[39m   }\n \u001b[90m 77 |\u001b[39m\u001b[0m\n\u001b[2m    at ChannelManagementPage.deleteChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:74:21)\u001b[22m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:348:25\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:40:34.459Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持删除和恢复渠道-Mobile-Chrome/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持删除和恢复渠道-Mobile-Chrome/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 74}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-9aa2e1a7a2d9918c8a1b", "file": "channel_management.spec.ts", "line": 325, "column": 9}, {"title": "应该显示渠道列表", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 63, "parallelIndex": 3, "status": "failed", "duration": 5933, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveCount\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('[data-testid=\"channel-list\"] [data-testid^=\"channel-\"]')\nExpected: \u001b[32m2\u001b[39m\nReceived: \u001b[31m0\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveCount\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"channel-list\"] [data-testid^=\"channel-\"]')\u001b[22m\n\u001b[2m    9 × locator resolved to 0 elements\u001b[22m\n\u001b[2m      - unexpected value \"0\"\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveCount\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('[data-testid=\"channel-list\"] [data-testid^=\"channel-\"]')\nExpected: \u001b[32m2\u001b[39m\nReceived: \u001b[31m0\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveCount\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"channel-list\"] [data-testid^=\"channel-\"]')\u001b[22m\n\u001b[2m    9 × locator resolved to 0 elements\u001b[22m\n\u001b[2m      - unexpected value \"0\"\u001b[22m\n\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:292:30", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 30, "line": 292}, "snippet": "\u001b[0m \u001b[90m 290 |\u001b[39m       \u001b[90m// 验证渠道列表显示\u001b[39m\n \u001b[90m 291 |\u001b[39m       \u001b[36mconst\u001b[39m channels \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m channelPage\u001b[33m.\u001b[39mgetChannelList()\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 292 |\u001b[39m       \u001b[36mawait\u001b[39m expect(channels)\u001b[33m.\u001b[39mtoHaveCount(\u001b[35m2\u001b[39m)\n \u001b[90m     |\u001b[39m                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 293 |\u001b[39m\n \u001b[90m 294 |\u001b[39m       \u001b[90m// 验证渠道信息\u001b[39m\n \u001b[90m 295 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"channel-channel-1\"]'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'主店铺'\u001b[39m)\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 30, "line": 292}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveCount\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('[data-testid=\"channel-list\"] [data-testid^=\"channel-\"]')\nExpected: \u001b[32m2\u001b[39m\nReceived: \u001b[31m0\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveCount\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"channel-list\"] [data-testid^=\"channel-\"]')\u001b[22m\n\u001b[2m    9 × locator resolved to 0 elements\u001b[22m\n\u001b[2m      - unexpected value \"0\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 290 |\u001b[39m       \u001b[90m// 验证渠道列表显示\u001b[39m\n \u001b[90m 291 |\u001b[39m       \u001b[36mconst\u001b[39m channels \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m channelPage\u001b[33m.\u001b[39mgetChannelList()\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 292 |\u001b[39m       \u001b[36mawait\u001b[39m expect(channels)\u001b[33m.\u001b[39mtoHaveCount(\u001b[35m2\u001b[39m)\n \u001b[90m     |\u001b[39m                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 293 |\u001b[39m\n \u001b[90m 294 |\u001b[39m       \u001b[90m// 验证渠道信息\u001b[39m\n \u001b[90m 295 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"channel-channel-1\"]'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'主店铺'\u001b[39m)\u001b[0m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:292:30\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:40:53.679Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该显示渠道列表-Mobile-Safari/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该显示渠道列表-Mobile-Safari/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 30, "line": 292}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-0a780b2e9c520b2d14ed", "file": "channel_management.spec.ts", "line": 287, "column": 9}, {"title": "应该支持搜索功能", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 64, "parallelIndex": 1, "status": "failed", "duration": 10939, "error": {"message": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"search-input\"]')\u001b[22m\n", "stack": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"search-input\"]')\u001b[22m\n\n    at ChannelManagementPage.searchChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:63:21)\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:307:25", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 63}, "snippet": "\u001b[0m \u001b[90m 61 |\u001b[39m   \u001b[90m// 搜索渠道\u001b[39m\n \u001b[90m 62 |\u001b[39m   \u001b[36masync\u001b[39m searchChannel(query\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 63 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"search-input\"]'\u001b[39m\u001b[33m,\u001b[39m query)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 64 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mkeyboard\u001b[33m.\u001b[39mpress(\u001b[32m'Enter'\u001b[39m)\n \u001b[90m 65 |\u001b[39m   }\n \u001b[90m 66 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 63}, "message": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"search-input\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 61 |\u001b[39m   \u001b[90m// 搜索渠道\u001b[39m\n \u001b[90m 62 |\u001b[39m   \u001b[36masync\u001b[39m searchChannel(query\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 63 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mfill(\u001b[32m'[data-testid=\"search-input\"]'\u001b[39m\u001b[33m,\u001b[39m query)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 64 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mkeyboard\u001b[33m.\u001b[39mpress(\u001b[32m'Enter'\u001b[39m)\n \u001b[90m 65 |\u001b[39m   }\n \u001b[90m 66 |\u001b[39m\u001b[0m\n\u001b[2m    at ChannelManagementPage.searchChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:63:21)\u001b[22m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:307:25\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:40:55.025Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持搜索功能-Mobile-Safari/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持搜索功能-Mobile-Safari/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 63}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-ee1452f168775a6477cd", "file": "channel_management.spec.ts", "line": 303, "column": 9}, {"title": "应该支持状态筛选", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 65, "parallelIndex": 2, "status": "failed", "duration": 10944, "error": {"message": "TimeoutError: page.selectOption: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"status-filter\"]')\u001b[22m\n", "stack": "TimeoutError: page.selectOption: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"status-filter\"]')\u001b[22m\n\n    at ChannelManagementPage.filterByStatus (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:69:21)\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:318:25", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 69}, "snippet": "\u001b[0m \u001b[90m 67 |\u001b[39m   \u001b[90m// 筛选渠道\u001b[39m\n \u001b[90m 68 |\u001b[39m   \u001b[36masync\u001b[39m filterByStatus(status\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 69 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mselectOption(\u001b[32m'[data-testid=\"status-filter\"]'\u001b[39m\u001b[33m,\u001b[39m status)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 70 |\u001b[39m   }\n \u001b[90m 71 |\u001b[39m\n \u001b[90m 72 |\u001b[39m   \u001b[90m// 删除渠道\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 69}, "message": "TimeoutError: page.selectOption: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"status-filter\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 67 |\u001b[39m   \u001b[90m// 筛选渠道\u001b[39m\n \u001b[90m 68 |\u001b[39m   \u001b[36masync\u001b[39m filterByStatus(status\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 69 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mselectOption(\u001b[32m'[data-testid=\"status-filter\"]'\u001b[39m\u001b[33m,\u001b[39m status)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 70 |\u001b[39m   }\n \u001b[90m 71 |\u001b[39m\n \u001b[90m 72 |\u001b[39m   \u001b[90m// 删除渠道\u001b[39m\u001b[0m\n\u001b[2m    at ChannelManagementPage.filterByStatus (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:69:21)\u001b[22m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:318:25\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:40:55.684Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持状态筛选-Mobile-Safari/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持状态筛选-Mobile-Safari/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 69}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-5b1ad406b63e4f05fcf1", "file": "channel_management.spec.ts", "line": 314, "column": 9}, {"title": "应该支持删除和恢复渠道", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 66, "parallelIndex": 3, "status": "failed", "duration": 10968, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"channel-channel-1\"] [data-testid=\"delete-button\"]')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"channel-channel-1\"] [data-testid=\"delete-button\"]')\u001b[22m\n\n    at ChannelManagementPage.deleteChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:74:21)\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:348:25", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 74}, "snippet": "\u001b[0m \u001b[90m 72 |\u001b[39m   \u001b[90m// 删除渠道\u001b[39m\n \u001b[90m 73 |\u001b[39m   \u001b[36masync\u001b[39m deleteChannel(channelId\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 74 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m`[data-testid=\"channel-${channelId}\"] [data-testid=\"delete-button\"]`\u001b[39m)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 75 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"confirm-delete\"]'\u001b[39m)\n \u001b[90m 76 |\u001b[39m   }\n \u001b[90m 77 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 74}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"channel-channel-1\"] [data-testid=\"delete-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 72 |\u001b[39m   \u001b[90m// 删除渠道\u001b[39m\n \u001b[90m 73 |\u001b[39m   \u001b[36masync\u001b[39m deleteChannel(channelId\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 74 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m`[data-testid=\"channel-${channelId}\"] [data-testid=\"delete-button\"]`\u001b[39m)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 75 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m'[data-testid=\"confirm-delete\"]'\u001b[39m)\n \u001b[90m 76 |\u001b[39m   }\n \u001b[90m 77 |\u001b[39m\u001b[0m\n\u001b[2m    at ChannelManagementPage.deleteChannel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:74:21)\u001b[22m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:348:25\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:41:00.008Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持删除和恢复渠道-Mobile-Safari/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-渠道管理操作-应该支持删除和恢复渠道-Mobile-Safari/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 74}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-9b798c7db4bdac7da628", "file": "channel_management.spec.ts", "line": 325, "column": 9}]}, {"title": "状态监控功能", "file": "channel_management.spec.ts", "line": 364, "column": 8, "specs": [{"title": "应该显示实时状态监控", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 7, "parallelIndex": 1, "status": "failed", "duration": 11321, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"channel-channel-1\"] [data-testid=\"monitor-button\"]')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"channel-channel-1\"] [data-testid=\"monitor-button\"]')\u001b[22m\n\n    at ChannelManagementPage.openMonitorPanel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:86:21)\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:401:25", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 86}, "snippet": "\u001b[0m \u001b[90m 84 |\u001b[39m   \u001b[90m// 打开监控面板\u001b[39m\n \u001b[90m 85 |\u001b[39m   \u001b[36masync\u001b[39m openMonitorPanel(channelId\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 86 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m`[data-testid=\"channel-${channelId}\"] [data-testid=\"monitor-button\"]`\u001b[39m)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 87 |\u001b[39m   }\n \u001b[90m 88 |\u001b[39m\n \u001b[90m 89 |\u001b[39m   \u001b[90m// 重连渠道\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 86}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"channel-channel-1\"] [data-testid=\"monitor-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 84 |\u001b[39m   \u001b[90m// 打开监控面板\u001b[39m\n \u001b[90m 85 |\u001b[39m   \u001b[36masync\u001b[39m openMonitorPanel(channelId\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 86 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m`[data-testid=\"channel-${channelId}\"] [data-testid=\"monitor-button\"]`\u001b[39m)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 87 |\u001b[39m   }\n \u001b[90m 88 |\u001b[39m\n \u001b[90m 89 |\u001b[39m   \u001b[90m// 重连渠道\u001b[39m\u001b[0m\n\u001b[2m    at ChannelManagementPage.openMonitorPanel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:86:21)\u001b[22m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:401:25\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:39:15.841Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-状态监控功能-应该显示实时状态监控-chromium/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-状态监控功能-应该显示实时状态监控-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 86}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-cdbb9bb0cdb309e18f86", "file": "channel_management.spec.ts", "line": 365, "column": 9}, {"title": "应该支持手动重连", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 8, "parallelIndex": 4, "status": "failed", "duration": 11332, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"channel-channel-2\"] [data-testid=\"monitor-button\"]')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"channel-channel-2\"] [data-testid=\"monitor-button\"]')\u001b[22m\n\n    at ChannelManagementPage.openMonitorPanel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:86:21)\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:418:25", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 86}, "snippet": "\u001b[0m \u001b[90m 84 |\u001b[39m   \u001b[90m// 打开监控面板\u001b[39m\n \u001b[90m 85 |\u001b[39m   \u001b[36masync\u001b[39m openMonitorPanel(channelId\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 86 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m`[data-testid=\"channel-${channelId}\"] [data-testid=\"monitor-button\"]`\u001b[39m)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 87 |\u001b[39m   }\n \u001b[90m 88 |\u001b[39m\n \u001b[90m 89 |\u001b[39m   \u001b[90m// 重连渠道\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 86}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"channel-channel-2\"] [data-testid=\"monitor-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 84 |\u001b[39m   \u001b[90m// 打开监控面板\u001b[39m\n \u001b[90m 85 |\u001b[39m   \u001b[36masync\u001b[39m openMonitorPanel(channelId\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 86 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m`[data-testid=\"channel-${channelId}\"] [data-testid=\"monitor-button\"]`\u001b[39m)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 87 |\u001b[39m   }\n \u001b[90m 88 |\u001b[39m\n \u001b[90m 89 |\u001b[39m   \u001b[90m// 重连渠道\u001b[39m\u001b[0m\n\u001b[2m    at ChannelManagementPage.openMonitorPanel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:86:21)\u001b[22m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:418:25\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:39:15.911Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-状态监控功能-应该支持手动重连-chromium/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-状态监控功能-应该支持手动重连-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 86}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-7e1519e0765994dc2639", "file": "channel_management.spec.ts", "line": 411, "column": 9}, {"title": "应该显示实时状态监控", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 22, "parallelIndex": 2, "status": "failed", "duration": 11377, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"channel-channel-1\"] [data-testid=\"monitor-button\"]')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"channel-channel-1\"] [data-testid=\"monitor-button\"]')\u001b[22m\n\n    at ChannelManagementPage.openMonitorPanel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:86:21)\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:401:25", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 86}, "snippet": "\u001b[0m \u001b[90m 84 |\u001b[39m   \u001b[90m// 打开监控面板\u001b[39m\n \u001b[90m 85 |\u001b[39m   \u001b[36masync\u001b[39m openMonitorPanel(channelId\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 86 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m`[data-testid=\"channel-${channelId}\"] [data-testid=\"monitor-button\"]`\u001b[39m)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 87 |\u001b[39m   }\n \u001b[90m 88 |\u001b[39m\n \u001b[90m 89 |\u001b[39m   \u001b[90m// 重连渠道\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 86}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"channel-channel-1\"] [data-testid=\"monitor-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 84 |\u001b[39m   \u001b[90m// 打开监控面板\u001b[39m\n \u001b[90m 85 |\u001b[39m   \u001b[36masync\u001b[39m openMonitorPanel(channelId\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 86 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m`[data-testid=\"channel-${channelId}\"] [data-testid=\"monitor-button\"]`\u001b[39m)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 87 |\u001b[39m   }\n \u001b[90m 88 |\u001b[39m\n \u001b[90m 89 |\u001b[39m   \u001b[90m// 重连渠道\u001b[39m\u001b[0m\n\u001b[2m    at ChannelManagementPage.openMonitorPanel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:86:21)\u001b[22m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:401:25\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:39:43.620Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-状态监控功能-应该显示实时状态监控-firefox/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-状态监控功能-应该显示实时状态监控-firefox/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 86}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-e95da40dcee96fd41e12", "file": "channel_management.spec.ts", "line": 365, "column": 9}, {"title": "应该支持手动重连", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 23, "parallelIndex": 3, "status": "failed", "duration": 11439, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"channel-channel-2\"] [data-testid=\"monitor-button\"]')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"channel-channel-2\"] [data-testid=\"monitor-button\"]')\u001b[22m\n\n    at ChannelManagementPage.openMonitorPanel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:86:21)\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:418:25", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 86}, "snippet": "\u001b[0m \u001b[90m 84 |\u001b[39m   \u001b[90m// 打开监控面板\u001b[39m\n \u001b[90m 85 |\u001b[39m   \u001b[36masync\u001b[39m openMonitorPanel(channelId\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 86 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m`[data-testid=\"channel-${channelId}\"] [data-testid=\"monitor-button\"]`\u001b[39m)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 87 |\u001b[39m   }\n \u001b[90m 88 |\u001b[39m\n \u001b[90m 89 |\u001b[39m   \u001b[90m// 重连渠道\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 86}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"channel-channel-2\"] [data-testid=\"monitor-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 84 |\u001b[39m   \u001b[90m// 打开监控面板\u001b[39m\n \u001b[90m 85 |\u001b[39m   \u001b[36masync\u001b[39m openMonitorPanel(channelId\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 86 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m`[data-testid=\"channel-${channelId}\"] [data-testid=\"monitor-button\"]`\u001b[39m)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 87 |\u001b[39m   }\n \u001b[90m 88 |\u001b[39m\n \u001b[90m 89 |\u001b[39m   \u001b[90m// 重连渠道\u001b[39m\u001b[0m\n\u001b[2m    at ChannelManagementPage.openMonitorPanel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:86:21)\u001b[22m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:418:25\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:39:44.001Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-状态监控功能-应该支持手动重连-firefox/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-状态监控功能-应该支持手动重连-firefox/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 86}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-edf4c574391ea18a1ba2", "file": "channel_management.spec.ts", "line": 411, "column": 9}, {"title": "应该显示实时状态监控", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 37, "parallelIndex": 0, "status": "failed", "duration": 10951, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"channel-channel-1\"] [data-testid=\"monitor-button\"]')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"channel-channel-1\"] [data-testid=\"monitor-button\"]')\u001b[22m\n\n    at ChannelManagementPage.openMonitorPanel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:86:21)\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:401:25", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 86}, "snippet": "\u001b[0m \u001b[90m 84 |\u001b[39m   \u001b[90m// 打开监控面板\u001b[39m\n \u001b[90m 85 |\u001b[39m   \u001b[36masync\u001b[39m openMonitorPanel(channelId\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 86 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m`[data-testid=\"channel-${channelId}\"] [data-testid=\"monitor-button\"]`\u001b[39m)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 87 |\u001b[39m   }\n \u001b[90m 88 |\u001b[39m\n \u001b[90m 89 |\u001b[39m   \u001b[90m// 重连渠道\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 86}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"channel-channel-1\"] [data-testid=\"monitor-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 84 |\u001b[39m   \u001b[90m// 打开监控面板\u001b[39m\n \u001b[90m 85 |\u001b[39m   \u001b[36masync\u001b[39m openMonitorPanel(channelId\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 86 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m`[data-testid=\"channel-${channelId}\"] [data-testid=\"monitor-button\"]`\u001b[39m)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 87 |\u001b[39m   }\n \u001b[90m 88 |\u001b[39m\n \u001b[90m 89 |\u001b[39m   \u001b[90m// 重连渠道\u001b[39m\u001b[0m\n\u001b[2m    at ChannelManagementPage.openMonitorPanel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:86:21)\u001b[22m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:401:25\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:40:09.832Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-状态监控功能-应该显示实时状态监控-webkit/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-状态监控功能-应该显示实时状态监控-webkit/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 86}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-1b3e1247faf5c52b5720", "file": "channel_management.spec.ts", "line": 365, "column": 9}, {"title": "应该支持手动重连", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 38, "parallelIndex": 2, "status": "failed", "duration": 10924, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"channel-channel-2\"] [data-testid=\"monitor-button\"]')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"channel-channel-2\"] [data-testid=\"monitor-button\"]')\u001b[22m\n\n    at ChannelManagementPage.openMonitorPanel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:86:21)\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:418:25", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 86}, "snippet": "\u001b[0m \u001b[90m 84 |\u001b[39m   \u001b[90m// 打开监控面板\u001b[39m\n \u001b[90m 85 |\u001b[39m   \u001b[36masync\u001b[39m openMonitorPanel(channelId\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 86 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m`[data-testid=\"channel-${channelId}\"] [data-testid=\"monitor-button\"]`\u001b[39m)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 87 |\u001b[39m   }\n \u001b[90m 88 |\u001b[39m\n \u001b[90m 89 |\u001b[39m   \u001b[90m// 重连渠道\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 86}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"channel-channel-2\"] [data-testid=\"monitor-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 84 |\u001b[39m   \u001b[90m// 打开监控面板\u001b[39m\n \u001b[90m 85 |\u001b[39m   \u001b[36masync\u001b[39m openMonitorPanel(channelId\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 86 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m`[data-testid=\"channel-${channelId}\"] [data-testid=\"monitor-button\"]`\u001b[39m)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 87 |\u001b[39m   }\n \u001b[90m 88 |\u001b[39m\n \u001b[90m 89 |\u001b[39m   \u001b[90m// 重连渠道\u001b[39m\u001b[0m\n\u001b[2m    at ChannelManagementPage.openMonitorPanel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:86:21)\u001b[22m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:418:25\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:40:10.020Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-状态监控功能-应该支持手动重连-webkit/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-状态监控功能-应该支持手动重连-webkit/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 86}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-30f89d1df32fed1b2864", "file": "channel_management.spec.ts", "line": 411, "column": 9}, {"title": "应该显示实时状态监控", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 52, "parallelIndex": 2, "status": "failed", "duration": 11076, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"channel-channel-1\"] [data-testid=\"monitor-button\"]')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"channel-channel-1\"] [data-testid=\"monitor-button\"]')\u001b[22m\n\n    at ChannelManagementPage.openMonitorPanel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:86:21)\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:401:25", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 86}, "snippet": "\u001b[0m \u001b[90m 84 |\u001b[39m   \u001b[90m// 打开监控面板\u001b[39m\n \u001b[90m 85 |\u001b[39m   \u001b[36masync\u001b[39m openMonitorPanel(channelId\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 86 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m`[data-testid=\"channel-${channelId}\"] [data-testid=\"monitor-button\"]`\u001b[39m)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 87 |\u001b[39m   }\n \u001b[90m 88 |\u001b[39m\n \u001b[90m 89 |\u001b[39m   \u001b[90m// 重连渠道\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 86}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"channel-channel-1\"] [data-testid=\"monitor-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 84 |\u001b[39m   \u001b[90m// 打开监控面板\u001b[39m\n \u001b[90m 85 |\u001b[39m   \u001b[36masync\u001b[39m openMonitorPanel(channelId\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 86 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m`[data-testid=\"channel-${channelId}\"] [data-testid=\"monitor-button\"]`\u001b[39m)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 87 |\u001b[39m   }\n \u001b[90m 88 |\u001b[39m\n \u001b[90m 89 |\u001b[39m   \u001b[90m// 重连渠道\u001b[39m\u001b[0m\n\u001b[2m    at ChannelManagementPage.openMonitorPanel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:86:21)\u001b[22m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:401:25\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:40:35.170Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-状态监控功能-应该显示实时状态监控-Mobile-Chrome/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-状态监控功能-应该显示实时状态监控-Mobile-Chrome/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 86}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-af09814699c1e94a0562", "file": "channel_management.spec.ts", "line": 365, "column": 9}, {"title": "应该支持手动重连", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 53, "parallelIndex": 4, "status": "failed", "duration": 11048, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"channel-channel-2\"] [data-testid=\"monitor-button\"]')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"channel-channel-2\"] [data-testid=\"monitor-button\"]')\u001b[22m\n\n    at ChannelManagementPage.openMonitorPanel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:86:21)\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:418:25", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 86}, "snippet": "\u001b[0m \u001b[90m 84 |\u001b[39m   \u001b[90m// 打开监控面板\u001b[39m\n \u001b[90m 85 |\u001b[39m   \u001b[36masync\u001b[39m openMonitorPanel(channelId\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 86 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m`[data-testid=\"channel-${channelId}\"] [data-testid=\"monitor-button\"]`\u001b[39m)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 87 |\u001b[39m   }\n \u001b[90m 88 |\u001b[39m\n \u001b[90m 89 |\u001b[39m   \u001b[90m// 重连渠道\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 86}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"channel-channel-2\"] [data-testid=\"monitor-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 84 |\u001b[39m   \u001b[90m// 打开监控面板\u001b[39m\n \u001b[90m 85 |\u001b[39m   \u001b[36masync\u001b[39m openMonitorPanel(channelId\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 86 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m`[data-testid=\"channel-${channelId}\"] [data-testid=\"monitor-button\"]`\u001b[39m)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 87 |\u001b[39m   }\n \u001b[90m 88 |\u001b[39m\n \u001b[90m 89 |\u001b[39m   \u001b[90m// 重连渠道\u001b[39m\u001b[0m\n\u001b[2m    at ChannelManagementPage.openMonitorPanel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:86:21)\u001b[22m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:418:25\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:40:37.319Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-状态监控功能-应该支持手动重连-Mobile-Chrome/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-状态监控功能-应该支持手动重连-Mobile-Chrome/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 86}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-6ae91f052edd4e45a4fc", "file": "channel_management.spec.ts", "line": 411, "column": 9}, {"title": "应该显示实时状态监控", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 67, "parallelIndex": 4, "status": "failed", "duration": 10931, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"channel-channel-1\"] [data-testid=\"monitor-button\"]')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"channel-channel-1\"] [data-testid=\"monitor-button\"]')\u001b[22m\n\n    at ChannelManagementPage.openMonitorPanel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:86:21)\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:401:25", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 86}, "snippet": "\u001b[0m \u001b[90m 84 |\u001b[39m   \u001b[90m// 打开监控面板\u001b[39m\n \u001b[90m 85 |\u001b[39m   \u001b[36masync\u001b[39m openMonitorPanel(channelId\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 86 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m`[data-testid=\"channel-${channelId}\"] [data-testid=\"monitor-button\"]`\u001b[39m)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 87 |\u001b[39m   }\n \u001b[90m 88 |\u001b[39m\n \u001b[90m 89 |\u001b[39m   \u001b[90m// 重连渠道\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 86}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"channel-channel-1\"] [data-testid=\"monitor-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 84 |\u001b[39m   \u001b[90m// 打开监控面板\u001b[39m\n \u001b[90m 85 |\u001b[39m   \u001b[36masync\u001b[39m openMonitorPanel(channelId\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 86 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m`[data-testid=\"channel-${channelId}\"] [data-testid=\"monitor-button\"]`\u001b[39m)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 87 |\u001b[39m   }\n \u001b[90m 88 |\u001b[39m\n \u001b[90m 89 |\u001b[39m   \u001b[90m// 重连渠道\u001b[39m\u001b[0m\n\u001b[2m    at ChannelManagementPage.openMonitorPanel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:86:21)\u001b[22m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:401:25\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:41:00.684Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-状态监控功能-应该显示实时状态监控-Mobile-Safari/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-状态监控功能-应该显示实时状态监控-Mobile-Safari/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 86}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-f55add63284b42086917", "file": "channel_management.spec.ts", "line": 365, "column": 9}, {"title": "应该支持手动重连", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 68, "parallelIndex": 0, "status": "failed", "duration": 10901, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"channel-channel-2\"] [data-testid=\"monitor-button\"]')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"channel-channel-2\"] [data-testid=\"monitor-button\"]')\u001b[22m\n\n    at ChannelManagementPage.openMonitorPanel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:86:21)\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:418:25", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 86}, "snippet": "\u001b[0m \u001b[90m 84 |\u001b[39m   \u001b[90m// 打开监控面板\u001b[39m\n \u001b[90m 85 |\u001b[39m   \u001b[36masync\u001b[39m openMonitorPanel(channelId\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 86 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m`[data-testid=\"channel-${channelId}\"] [data-testid=\"monitor-button\"]`\u001b[39m)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 87 |\u001b[39m   }\n \u001b[90m 88 |\u001b[39m\n \u001b[90m 89 |\u001b[39m   \u001b[90m// 重连渠道\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 86}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-testid=\"channel-channel-2\"] [data-testid=\"monitor-button\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 84 |\u001b[39m   \u001b[90m// 打开监控面板\u001b[39m\n \u001b[90m 85 |\u001b[39m   \u001b[36masync\u001b[39m openMonitorPanel(channelId\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 86 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mclick(\u001b[32m`[data-testid=\"channel-${channelId}\"] [data-testid=\"monitor-button\"]`\u001b[39m)\n \u001b[90m    |\u001b[39m                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 87 |\u001b[39m   }\n \u001b[90m 88 |\u001b[39m\n \u001b[90m 89 |\u001b[39m   \u001b[90m// 重连渠道\u001b[39m\u001b[0m\n\u001b[2m    at ChannelManagementPage.openMonitorPanel (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:86:21)\u001b[22m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:418:25\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:41:04.095Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-状态监控功能-应该支持手动重连-Mobile-Safari/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-状态监控功能-应该支持手动重连-Mobile-Safari/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 21, "line": 86}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-05816565f4a5cc022175", "file": "channel_management.spec.ts", "line": 411, "column": 9}]}, {"title": "错误处理场景", "file": "channel_management.spec.ts", "line": 432, "column": 8, "specs": [{"title": "应该处理网络错误", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 9, "parallelIndex": 2, "status": "failed", "duration": 6327, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"network-error\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"network-error\"]')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"network-error\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"network-error\"]')\u001b[22m\n\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:442:67", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 67, "line": 442}, "snippet": "\u001b[0m \u001b[90m 440 |\u001b[39m\n \u001b[90m 441 |\u001b[39m       \u001b[90m// 验证错误提示\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 442 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"network-error\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 443 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"retry-button\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m 444 |\u001b[39m\n \u001b[90m 445 |\u001b[39m       \u001b[90m// 点击重试\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 67, "line": 442}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"network-error\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"network-error\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 440 |\u001b[39m\n \u001b[90m 441 |\u001b[39m       \u001b[90m// 验证错误提示\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 442 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"network-error\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 443 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"retry-button\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m 444 |\u001b[39m\n \u001b[90m 445 |\u001b[39m       \u001b[90m// 点击重试\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:442:67\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:39:15.946Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理网络错误-chromium/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理网络错误-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 67, "line": 442}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-4bc6ec41c08e96be7708", "file": "channel_management.spec.ts", "line": 433, "column": 9}, {"title": "应该处理服务器错误", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 10, "parallelIndex": 3, "status": "failed", "duration": 6524, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"server-error\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"server-error\"]')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"server-error\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"server-error\"]')\u001b[22m\n\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:464:66", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 66, "line": 464}, "snippet": "\u001b[0m \u001b[90m 462 |\u001b[39m\n \u001b[90m 463 |\u001b[39m       \u001b[90m// 验证错误提示\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 464 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"server-error\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 465 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"error-message\"]'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'服务器内部错误'\u001b[39m)\n \u001b[90m 466 |\u001b[39m     })\n \u001b[90m 467 |\u001b[39m   })\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 66, "line": 464}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"server-error\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"server-error\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 462 |\u001b[39m\n \u001b[90m 463 |\u001b[39m       \u001b[90m// 验证错误提示\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 464 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"server-error\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 465 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"error-message\"]'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'服务器内部错误'\u001b[39m)\n \u001b[90m 466 |\u001b[39m     })\n \u001b[90m 467 |\u001b[39m   })\u001b[0m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:464:66\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:39:22.952Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理服务器错误-chromium/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理服务器错误-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 66, "line": 464}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-0707ff4338d364defb9c", "file": "channel_management.spec.ts", "line": 449, "column": 9}, {"title": "应该处理网络错误", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 24, "parallelIndex": 4, "status": "failed", "duration": 6365, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"network-error\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"network-error\"]')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"network-error\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"network-error\"]')\u001b[22m\n\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:442:67", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 67, "line": 442}, "snippet": "\u001b[0m \u001b[90m 440 |\u001b[39m\n \u001b[90m 441 |\u001b[39m       \u001b[90m// 验证错误提示\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 442 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"network-error\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 443 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"retry-button\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m 444 |\u001b[39m\n \u001b[90m 445 |\u001b[39m       \u001b[90m// 点击重试\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 67, "line": 442}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"network-error\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"network-error\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 440 |\u001b[39m\n \u001b[90m 441 |\u001b[39m       \u001b[90m// 验证错误提示\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 442 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"network-error\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 443 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"retry-button\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m 444 |\u001b[39m\n \u001b[90m 445 |\u001b[39m       \u001b[90m// 点击重试\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:442:67\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:39:47.277Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理网络错误-firefox/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理网络错误-firefox/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 67, "line": 442}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-b93c2ae011322c927a44", "file": "channel_management.spec.ts", "line": 433, "column": 9}, {"title": "应该处理服务器错误", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 25, "parallelIndex": 1, "status": "failed", "duration": 6374, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"server-error\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"server-error\"]')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"server-error\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"server-error\"]')\u001b[22m\n\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:464:66", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 66, "line": 464}, "snippet": "\u001b[0m \u001b[90m 462 |\u001b[39m\n \u001b[90m 463 |\u001b[39m       \u001b[90m// 验证错误提示\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 464 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"server-error\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 465 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"error-message\"]'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'服务器内部错误'\u001b[39m)\n \u001b[90m 466 |\u001b[39m     })\n \u001b[90m 467 |\u001b[39m   })\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 66, "line": 464}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"server-error\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"server-error\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 462 |\u001b[39m\n \u001b[90m 463 |\u001b[39m       \u001b[90m// 验证错误提示\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 464 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"server-error\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 465 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"error-message\"]'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'服务器内部错误'\u001b[39m)\n \u001b[90m 466 |\u001b[39m     })\n \u001b[90m 467 |\u001b[39m   })\u001b[0m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:464:66\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:39:50.219Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理服务器错误-firefox/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理服务器错误-firefox/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 66, "line": 464}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-4f282de9204644be731e", "file": "channel_management.spec.ts", "line": 449, "column": 9}, {"title": "应该处理网络错误", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 39, "parallelIndex": 3, "status": "failed", "duration": 5897, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"network-error\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"network-error\"]')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"network-error\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"network-error\"]')\u001b[22m\n\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:442:67", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 67, "line": 442}, "snippet": "\u001b[0m \u001b[90m 440 |\u001b[39m\n \u001b[90m 441 |\u001b[39m       \u001b[90m// 验证错误提示\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 442 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"network-error\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 443 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"retry-button\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m 444 |\u001b[39m\n \u001b[90m 445 |\u001b[39m       \u001b[90m// 点击重试\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 67, "line": 442}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"network-error\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"network-error\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 440 |\u001b[39m\n \u001b[90m 441 |\u001b[39m       \u001b[90m// 验证错误提示\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 442 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"network-error\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 443 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"retry-button\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m 444 |\u001b[39m\n \u001b[90m 445 |\u001b[39m       \u001b[90m// 点击重试\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:442:67\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:40:15.185Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理网络错误-webkit/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理网络错误-webkit/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 67, "line": 442}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-64a30f5bb7b47e1fb2e4", "file": "channel_management.spec.ts", "line": 433, "column": 9}, {"title": "应该处理服务器错误", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 40, "parallelIndex": 1, "status": "failed", "duration": 5889, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"server-error\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"server-error\"]')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"server-error\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"server-error\"]')\u001b[22m\n\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:464:66", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 66, "line": 464}, "snippet": "\u001b[0m \u001b[90m 462 |\u001b[39m\n \u001b[90m 463 |\u001b[39m       \u001b[90m// 验证错误提示\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 464 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"server-error\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 465 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"error-message\"]'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'服务器内部错误'\u001b[39m)\n \u001b[90m 466 |\u001b[39m     })\n \u001b[90m 467 |\u001b[39m   })\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 66, "line": 464}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"server-error\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"server-error\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 462 |\u001b[39m\n \u001b[90m 463 |\u001b[39m       \u001b[90m// 验证错误提示\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 464 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"server-error\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 465 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"error-message\"]'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'服务器内部错误'\u001b[39m)\n \u001b[90m 466 |\u001b[39m     })\n \u001b[90m 467 |\u001b[39m   })\u001b[0m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:464:66\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:40:15.875Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理服务器错误-webkit/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理服务器错误-webkit/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 66, "line": 464}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-9689ccf40c9f24dc7d84", "file": "channel_management.spec.ts", "line": 449, "column": 9}, {"title": "应该处理网络错误", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 54, "parallelIndex": 3, "status": "failed", "duration": 6148, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"network-error\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"network-error\"]')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"network-error\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"network-error\"]')\u001b[22m\n\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:442:67", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 67, "line": 442}, "snippet": "\u001b[0m \u001b[90m 440 |\u001b[39m\n \u001b[90m 441 |\u001b[39m       \u001b[90m// 验证错误提示\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 442 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"network-error\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 443 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"retry-button\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m 444 |\u001b[39m\n \u001b[90m 445 |\u001b[39m       \u001b[90m// 点击重试\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 67, "line": 442}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"network-error\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"network-error\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 440 |\u001b[39m\n \u001b[90m 441 |\u001b[39m       \u001b[90m// 验证错误提示\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 442 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"network-error\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 443 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"retry-button\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m 444 |\u001b[39m\n \u001b[90m 445 |\u001b[39m       \u001b[90m// 点击重试\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:442:67\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:40:40.027Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理网络错误-Mobile-Chrome/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理网络错误-Mobile-Chrome/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 67, "line": 442}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-95fcd09f742ea8c7f66c", "file": "channel_management.spec.ts", "line": 433, "column": 9}, {"title": "应该处理服务器错误", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 55, "parallelIndex": 1, "status": "failed", "duration": 6017, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"server-error\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"server-error\"]')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"server-error\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"server-error\"]')\u001b[22m\n\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:464:66", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 66, "line": 464}, "snippet": "\u001b[0m \u001b[90m 462 |\u001b[39m\n \u001b[90m 463 |\u001b[39m       \u001b[90m// 验证错误提示\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 464 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"server-error\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 465 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"error-message\"]'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'服务器内部错误'\u001b[39m)\n \u001b[90m 466 |\u001b[39m     })\n \u001b[90m 467 |\u001b[39m   })\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 66, "line": 464}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"server-error\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"server-error\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 462 |\u001b[39m\n \u001b[90m 463 |\u001b[39m       \u001b[90m// 验证错误提示\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 464 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"server-error\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 465 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"error-message\"]'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'服务器内部错误'\u001b[39m)\n \u001b[90m 466 |\u001b[39m     })\n \u001b[90m 467 |\u001b[39m   })\u001b[0m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:464:66\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:40:41.708Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理服务器错误-Mobile-Chrome/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理服务器错误-Mobile-Chrome/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 66, "line": 464}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-04f70cdc7ab8c24c76c6", "file": "channel_management.spec.ts", "line": 449, "column": 9}, {"title": "应该处理网络错误", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 69, "parallelIndex": 1, "status": "failed", "duration": 5914, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"network-error\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"network-error\"]')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"network-error\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"network-error\"]')\u001b[22m\n\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:442:67", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 67, "line": 442}, "snippet": "\u001b[0m \u001b[90m 440 |\u001b[39m\n \u001b[90m 441 |\u001b[39m       \u001b[90m// 验证错误提示\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 442 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"network-error\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 443 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"retry-button\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m 444 |\u001b[39m\n \u001b[90m 445 |\u001b[39m       \u001b[90m// 点击重试\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 67, "line": 442}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"network-error\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"network-error\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 440 |\u001b[39m\n \u001b[90m 441 |\u001b[39m       \u001b[90m// 验证错误提示\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 442 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"network-error\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 443 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"retry-button\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m 444 |\u001b[39m\n \u001b[90m 445 |\u001b[39m       \u001b[90m// 点击重试\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:442:67\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:41:06.349Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理网络错误-Mobile-Safari/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理网络错误-Mobile-Safari/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 67, "line": 442}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-039666465b161a406f9d", "file": "channel_management.spec.ts", "line": 433, "column": 9}, {"title": "应该处理服务器错误", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 70, "parallelIndex": 2, "status": "failed", "duration": 5900, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"server-error\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"server-error\"]')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"server-error\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"server-error\"]')\u001b[22m\n\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:464:66", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 66, "line": 464}, "snippet": "\u001b[0m \u001b[90m 462 |\u001b[39m\n \u001b[90m 463 |\u001b[39m       \u001b[90m// 验证错误提示\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 464 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"server-error\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 465 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"error-message\"]'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'服务器内部错误'\u001b[39m)\n \u001b[90m 466 |\u001b[39m     })\n \u001b[90m 467 |\u001b[39m   })\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 66, "line": 464}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"server-error\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"server-error\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 462 |\u001b[39m\n \u001b[90m 463 |\u001b[39m       \u001b[90m// 验证错误提示\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 464 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"server-error\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 465 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"error-message\"]'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[32m'服务器内部错误'\u001b[39m)\n \u001b[90m 466 |\u001b[39m     })\n \u001b[90m 467 |\u001b[39m   })\u001b[0m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:464:66\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:41:07.033Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理服务器错误-Mobile-Safari/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-错误处理场景-应该处理服务器错误-Mobile-Safari/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 66, "line": 464}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-d30da3614492886b5172", "file": "channel_management.spec.ts", "line": 449, "column": 9}]}, {"title": "响应式设计测试", "file": "channel_management.spec.ts", "line": 469, "column": 8, "specs": [{"title": "应该在移动端正确显示", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 11, "parallelIndex": 2, "status": "failed", "duration": 6499, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"mobile-layout\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"mobile-layout\"]')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"mobile-layout\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"mobile-layout\"]')\u001b[22m\n\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:477:67", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 67, "line": 477}, "snippet": "\u001b[0m \u001b[90m 475 |\u001b[39m\n \u001b[90m 476 |\u001b[39m       \u001b[90m// 验证移动端布局\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 477 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"mobile-layout\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 478 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"mobile-menu\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m 479 |\u001b[39m\n \u001b[90m 480 |\u001b[39m       \u001b[90m// 验证响应式表格\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 67, "line": 477}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"mobile-layout\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"mobile-layout\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 475 |\u001b[39m\n \u001b[90m 476 |\u001b[39m       \u001b[90m// 验证移动端布局\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 477 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"mobile-layout\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 478 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"mobile-menu\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m 479 |\u001b[39m\n \u001b[90m 480 |\u001b[39m       \u001b[90m// 验证响应式表格\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:477:67\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:39:22.957Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在移动端正确显示-chromium/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在移动端正确显示-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 67, "line": 477}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-edc2005e773d56b28dd7", "file": "channel_management.spec.ts", "line": 470, "column": 9}, {"title": "应该在平板端正确显示", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 12, "parallelIndex": 0, "status": "failed", "duration": 6380, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"tablet-layout\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"tablet-layout\"]')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"tablet-layout\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"tablet-layout\"]')\u001b[22m\n\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:491:67", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 67, "line": 491}, "snippet": "\u001b[0m \u001b[90m 489 |\u001b[39m\n \u001b[90m 490 |\u001b[39m       \u001b[90m// 验证平板端布局\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 491 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"tablet-layout\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 492 |\u001b[39m     })\n \u001b[90m 493 |\u001b[39m   })\n \u001b[90m 494 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 67, "line": 491}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"tablet-layout\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"tablet-layout\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 489 |\u001b[39m\n \u001b[90m 490 |\u001b[39m       \u001b[90m// 验证平板端布局\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 491 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"tablet-layout\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 492 |\u001b[39m     })\n \u001b[90m 493 |\u001b[39m   })\n \u001b[90m 494 |\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:491:67\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:39:23.073Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在平板端正确显示-chromium/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在平板端正确显示-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 67, "line": 491}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-110d64c0af4f18ec47ac", "file": "channel_management.spec.ts", "line": 484, "column": 9}, {"title": "应该在移动端正确显示", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 26, "parallelIndex": 0, "status": "failed", "duration": 6388, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"mobile-layout\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"mobile-layout\"]')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"mobile-layout\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"mobile-layout\"]')\u001b[22m\n\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:477:67", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 67, "line": 477}, "snippet": "\u001b[0m \u001b[90m 475 |\u001b[39m\n \u001b[90m 476 |\u001b[39m       \u001b[90m// 验证移动端布局\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 477 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"mobile-layout\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 478 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"mobile-menu\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m 479 |\u001b[39m\n \u001b[90m 480 |\u001b[39m       \u001b[90m// 验证响应式表格\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 67, "line": 477}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"mobile-layout\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"mobile-layout\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 475 |\u001b[39m\n \u001b[90m 476 |\u001b[39m       \u001b[90m// 验证移动端布局\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 477 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"mobile-layout\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 478 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"mobile-menu\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m 479 |\u001b[39m\n \u001b[90m 480 |\u001b[39m       \u001b[90m// 验证响应式表格\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:477:67\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:39:50.821Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在移动端正确显示-firefox/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在移动端正确显示-firefox/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 67, "line": 477}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-dfe5f9adade2ae576bc6", "file": "channel_management.spec.ts", "line": 470, "column": 9}, {"title": "应该在平板端正确显示", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 27, "parallelIndex": 4, "status": "failed", "duration": 6360, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"tablet-layout\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"tablet-layout\"]')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"tablet-layout\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"tablet-layout\"]')\u001b[22m\n\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:491:67", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 67, "line": 491}, "snippet": "\u001b[0m \u001b[90m 489 |\u001b[39m\n \u001b[90m 490 |\u001b[39m       \u001b[90m// 验证平板端布局\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 491 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"tablet-layout\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 492 |\u001b[39m     })\n \u001b[90m 493 |\u001b[39m   })\n \u001b[90m 494 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 67, "line": 491}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"tablet-layout\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"tablet-layout\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 489 |\u001b[39m\n \u001b[90m 490 |\u001b[39m       \u001b[90m// 验证平板端布局\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 491 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"tablet-layout\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 492 |\u001b[39m     })\n \u001b[90m 493 |\u001b[39m   })\n \u001b[90m 494 |\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:491:67\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:39:54.583Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在平板端正确显示-firefox/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在平板端正确显示-firefox/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 67, "line": 491}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-662965b3d5bd2a7d64ca", "file": "channel_management.spec.ts", "line": 484, "column": 9}, {"title": "应该在移动端正确显示", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 41, "parallelIndex": 4, "status": "failed", "duration": 5898, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"mobile-layout\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"mobile-layout\"]')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"mobile-layout\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"mobile-layout\"]')\u001b[22m\n\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:477:67", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 67, "line": 477}, "snippet": "\u001b[0m \u001b[90m 475 |\u001b[39m\n \u001b[90m 476 |\u001b[39m       \u001b[90m// 验证移动端布局\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 477 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"mobile-layout\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 478 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"mobile-menu\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m 479 |\u001b[39m\n \u001b[90m 480 |\u001b[39m       \u001b[90m// 验证响应式表格\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 67, "line": 477}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"mobile-layout\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"mobile-layout\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 475 |\u001b[39m\n \u001b[90m 476 |\u001b[39m       \u001b[90m// 验证移动端布局\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 477 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"mobile-layout\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 478 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"mobile-menu\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m 479 |\u001b[39m\n \u001b[90m 480 |\u001b[39m       \u001b[90m// 验证响应式表格\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:477:67\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:40:19.456Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在移动端正确显示-webkit/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在移动端正确显示-webkit/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 67, "line": 477}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-f7b8d50ea7747db93041", "file": "channel_management.spec.ts", "line": 470, "column": 9}, {"title": "应该在平板端正确显示", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 42, "parallelIndex": 0, "status": "failed", "duration": 6002, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"tablet-layout\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"tablet-layout\"]')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"tablet-layout\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"tablet-layout\"]')\u001b[22m\n\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:491:67", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 67, "line": 491}, "snippet": "\u001b[0m \u001b[90m 489 |\u001b[39m\n \u001b[90m 490 |\u001b[39m       \u001b[90m// 验证平板端布局\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 491 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"tablet-layout\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 492 |\u001b[39m     })\n \u001b[90m 493 |\u001b[39m   })\n \u001b[90m 494 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 67, "line": 491}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"tablet-layout\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"tablet-layout\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 489 |\u001b[39m\n \u001b[90m 490 |\u001b[39m       \u001b[90m// 验证平板端布局\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 491 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"tablet-layout\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 492 |\u001b[39m     })\n \u001b[90m 493 |\u001b[39m   })\n \u001b[90m 494 |\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:491:67\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:40:21.175Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在平板端正确显示-webkit/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在平板端正确显示-webkit/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 67, "line": 491}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-4d9d9543c10cbe5fe39a", "file": "channel_management.spec.ts", "line": 484, "column": 9}, {"title": "应该在移动端正确显示", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 56, "parallelIndex": 0, "status": "failed", "duration": 6081, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"mobile-layout\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"mobile-layout\"]')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"mobile-layout\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"mobile-layout\"]')\u001b[22m\n\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:477:67", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 67, "line": 477}, "snippet": "\u001b[0m \u001b[90m 475 |\u001b[39m\n \u001b[90m 476 |\u001b[39m       \u001b[90m// 验证移动端布局\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 477 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"mobile-layout\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 478 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"mobile-menu\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m 479 |\u001b[39m\n \u001b[90m 480 |\u001b[39m       \u001b[90m// 验证响应式表格\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 67, "line": 477}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"mobile-layout\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"mobile-layout\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 475 |\u001b[39m\n \u001b[90m 476 |\u001b[39m       \u001b[90m// 验证移动端布局\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 477 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"mobile-layout\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 478 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"mobile-menu\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m 479 |\u001b[39m\n \u001b[90m 480 |\u001b[39m       \u001b[90m// 验证响应式表格\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:477:67\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:40:46.169Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在移动端正确显示-Mobile-Chrome/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在移动端正确显示-Mobile-Chrome/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 67, "line": 477}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-37549dc9e82eaa85cf06", "file": "channel_management.spec.ts", "line": 470, "column": 9}, {"title": "应该在平板端正确显示", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 57, "parallelIndex": 3, "status": "failed", "duration": 6256, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"tablet-layout\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"tablet-layout\"]')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"tablet-layout\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"tablet-layout\"]')\u001b[22m\n\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:491:67", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 67, "line": 491}, "snippet": "\u001b[0m \u001b[90m 489 |\u001b[39m\n \u001b[90m 490 |\u001b[39m       \u001b[90m// 验证平板端布局\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 491 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"tablet-layout\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 492 |\u001b[39m     })\n \u001b[90m 493 |\u001b[39m   })\n \u001b[90m 494 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 67, "line": 491}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"tablet-layout\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"tablet-layout\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 489 |\u001b[39m\n \u001b[90m 490 |\u001b[39m       \u001b[90m// 验证平板端布局\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 491 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"tablet-layout\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 492 |\u001b[39m     })\n \u001b[90m 493 |\u001b[39m   })\n \u001b[90m 494 |\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:491:67\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:40:46.898Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在平板端正确显示-Mobile-Chrome/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在平板端正确显示-Mobile-Chrome/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 67, "line": 491}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-357bc52275dc2d8adab6", "file": "channel_management.spec.ts", "line": 484, "column": 9}, {"title": "应该在移动端正确显示", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 71, "parallelIndex": 3, "status": "failed", "duration": 5962, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"mobile-layout\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"mobile-layout\"]')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"mobile-layout\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"mobile-layout\"]')\u001b[22m\n\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:477:67", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 67, "line": 477}, "snippet": "\u001b[0m \u001b[90m 475 |\u001b[39m\n \u001b[90m 476 |\u001b[39m       \u001b[90m// 验证移动端布局\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 477 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"mobile-layout\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 478 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"mobile-menu\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m 479 |\u001b[39m\n \u001b[90m 480 |\u001b[39m       \u001b[90m// 验证响应式表格\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 67, "line": 477}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"mobile-layout\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"mobile-layout\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 475 |\u001b[39m\n \u001b[90m 476 |\u001b[39m       \u001b[90m// 验证移动端布局\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 477 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"mobile-layout\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 478 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"mobile-menu\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m 479 |\u001b[39m\n \u001b[90m 480 |\u001b[39m       \u001b[90m// 验证响应式表格\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:477:67\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:41:11.336Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在移动端正确显示-Mobile-Safari/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在移动端正确显示-Mobile-Safari/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 67, "line": 477}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-5f2c5047b6873c56c730", "file": "channel_management.spec.ts", "line": 470, "column": 9}, {"title": "应该在平板端正确显示", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 72, "parallelIndex": 4, "status": "failed", "duration": 5979, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"tablet-layout\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"tablet-layout\"]')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"tablet-layout\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"tablet-layout\"]')\u001b[22m\n\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:491:67", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 67, "line": 491}, "snippet": "\u001b[0m \u001b[90m 489 |\u001b[39m\n \u001b[90m 490 |\u001b[39m       \u001b[90m// 验证平板端布局\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 491 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"tablet-layout\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 492 |\u001b[39m     })\n \u001b[90m 493 |\u001b[39m   })\n \u001b[90m 494 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 67, "line": 491}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"tablet-layout\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"tablet-layout\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 489 |\u001b[39m\n \u001b[90m 490 |\u001b[39m       \u001b[90m// 验证平板端布局\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 491 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"tablet-layout\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 492 |\u001b[39m     })\n \u001b[90m 493 |\u001b[39m   })\n \u001b[90m 494 |\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:491:67\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:41:12.034Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在平板端正确显示-Mobile-Safari/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-响应式设计测试-应该在平板端正确显示-Mobile-Safari/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 67, "line": 491}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-60be6b89b4a32c6f7f1b", "file": "channel_management.spec.ts", "line": 484, "column": 9}]}, {"title": "性能测试", "file": "channel_management.spec.ts", "line": 495, "column": 8, "specs": [{"title": "页面加载性能应该符合要求", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 13, "parallelIndex": 1, "status": "passed", "duration": 1309, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:39:27.785Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "672dd40e39adbe973451-1ec7ea60331a0338d954", "file": "channel_management.spec.ts", "line": 496, "column": 9}, {"title": "大量数据渲染性能", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 14, "parallelIndex": 4, "status": "failed", "duration": 6352, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"channel-list\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"channel-list\"]')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"channel-list\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"channel-list\"]')\u001b[22m\n\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:540:66", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 66, "line": 540}, "snippet": "\u001b[0m \u001b[90m 538 |\u001b[39m       \n \u001b[90m 539 |\u001b[39m       \u001b[90m// 等待列表渲染完成\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 540 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"channel-list\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 541 |\u001b[39m       \n \u001b[90m 542 |\u001b[39m       \u001b[36mconst\u001b[39m renderTime \u001b[33m=\u001b[39m \u001b[33mDate\u001b[39m\u001b[33m.\u001b[39mnow() \u001b[33m-\u001b[39m startTime\n \u001b[90m 543 |\u001b[39m       \u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 66, "line": 540}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"channel-list\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"channel-list\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 538 |\u001b[39m       \n \u001b[90m 539 |\u001b[39m       \u001b[90m// 等待列表渲染完成\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 540 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"channel-list\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 541 |\u001b[39m       \n \u001b[90m 542 |\u001b[39m       \u001b[36mconst\u001b[39m renderTime \u001b[33m=\u001b[39m \u001b[33mDate\u001b[39m\u001b[33m.\u001b[39mnow() \u001b[33m-\u001b[39m startTime\n \u001b[90m 543 |\u001b[39m       \u001b[0m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:540:66\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:39:27.803Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-性能测试-大量数据渲染性能-chromium/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-性能测试-大量数据渲染性能-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 66, "line": 540}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-35415677ae14c3acd16a", "file": "channel_management.spec.ts", "line": 510, "column": 9}, {"title": "页面加载性能应该符合要求", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 28, "parallelIndex": 2, "status": "passed", "duration": 1435, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:39:56.037Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "672dd40e39adbe973451-c7b6857185f885f9ac60", "file": "channel_management.spec.ts", "line": 496, "column": 9}, {"title": "大量数据渲染性能", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 29, "parallelIndex": 3, "status": "failed", "duration": 6465, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"channel-list\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"channel-list\"]')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"channel-list\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"channel-list\"]')\u001b[22m\n\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:540:66", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 66, "line": 540}, "snippet": "\u001b[0m \u001b[90m 538 |\u001b[39m       \n \u001b[90m 539 |\u001b[39m       \u001b[90m// 等待列表渲染完成\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 540 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"channel-list\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 541 |\u001b[39m       \n \u001b[90m 542 |\u001b[39m       \u001b[36mconst\u001b[39m renderTime \u001b[33m=\u001b[39m \u001b[33mDate\u001b[39m\u001b[33m.\u001b[39mnow() \u001b[33m-\u001b[39m startTime\n \u001b[90m 543 |\u001b[39m       \u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 66, "line": 540}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"channel-list\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"channel-list\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 538 |\u001b[39m       \n \u001b[90m 539 |\u001b[39m       \u001b[90m// 等待列表渲染完成\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 540 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"channel-list\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 541 |\u001b[39m       \n \u001b[90m 542 |\u001b[39m       \u001b[36mconst\u001b[39m renderTime \u001b[33m=\u001b[39m \u001b[33mDate\u001b[39m\u001b[33m.\u001b[39mnow() \u001b[33m-\u001b[39m startTime\n \u001b[90m 543 |\u001b[39m       \u001b[0m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:540:66\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:39:56.436Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-性能测试-大量数据渲染性能-firefox/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-性能测试-大量数据渲染性能-firefox/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 66, "line": 540}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-1e7a1f9bd923e0fec28d", "file": "channel_management.spec.ts", "line": 510, "column": 9}, {"title": "页面加载性能应该符合要求", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 43, "parallelIndex": 2, "status": "passed", "duration": 986, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:40:21.378Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "672dd40e39adbe973451-ebf1a166529ffbf584ab", "file": "channel_management.spec.ts", "line": 496, "column": 9}, {"title": "大量数据渲染性能", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 44, "parallelIndex": 3, "status": "failed", "duration": 5993, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"channel-list\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"channel-list\"]')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"channel-list\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"channel-list\"]')\u001b[22m\n\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:540:66", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 66, "line": 540}, "snippet": "\u001b[0m \u001b[90m 538 |\u001b[39m       \n \u001b[90m 539 |\u001b[39m       \u001b[90m// 等待列表渲染完成\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 540 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"channel-list\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 541 |\u001b[39m       \n \u001b[90m 542 |\u001b[39m       \u001b[36mconst\u001b[39m renderTime \u001b[33m=\u001b[39m \u001b[33mDate\u001b[39m\u001b[33m.\u001b[39mnow() \u001b[33m-\u001b[39m startTime\n \u001b[90m 543 |\u001b[39m       \u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 66, "line": 540}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"channel-list\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"channel-list\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 538 |\u001b[39m       \n \u001b[90m 539 |\u001b[39m       \u001b[90m// 等待列表渲染完成\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 540 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"channel-list\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 541 |\u001b[39m       \n \u001b[90m 542 |\u001b[39m       \u001b[36mconst\u001b[39m renderTime \u001b[33m=\u001b[39m \u001b[33mDate\u001b[39m\u001b[33m.\u001b[39mnow() \u001b[33m-\u001b[39m startTime\n \u001b[90m 543 |\u001b[39m       \u001b[0m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:540:66\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:40:21.592Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-性能测试-大量数据渲染性能-webkit/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-性能测试-大量数据渲染性能-webkit/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 66, "line": 540}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-2ad18c41a82fe5151703", "file": "channel_management.spec.ts", "line": 510, "column": 9}, {"title": "页面加载性能应该符合要求", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 58, "parallelIndex": 2, "status": "passed", "duration": 1182, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:40:46.947Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "672dd40e39adbe973451-d775ca833e94c985630c", "file": "channel_management.spec.ts", "line": 496, "column": 9}, {"title": "大量数据渲染性能", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 59, "parallelIndex": 1, "status": "failed", "duration": 6118, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"channel-list\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"channel-list\"]')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"channel-list\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"channel-list\"]')\u001b[22m\n\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:540:66", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 66, "line": 540}, "snippet": "\u001b[0m \u001b[90m 538 |\u001b[39m       \n \u001b[90m 539 |\u001b[39m       \u001b[90m// 等待列表渲染完成\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 540 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"channel-list\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 541 |\u001b[39m       \n \u001b[90m 542 |\u001b[39m       \u001b[36mconst\u001b[39m renderTime \u001b[33m=\u001b[39m \u001b[33mDate\u001b[39m\u001b[33m.\u001b[39mnow() \u001b[33m-\u001b[39m startTime\n \u001b[90m 543 |\u001b[39m       \u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 66, "line": 540}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"channel-list\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"channel-list\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 538 |\u001b[39m       \n \u001b[90m 539 |\u001b[39m       \u001b[90m// 等待列表渲染完成\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 540 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"channel-list\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 541 |\u001b[39m       \n \u001b[90m 542 |\u001b[39m       \u001b[36mconst\u001b[39m renderTime \u001b[33m=\u001b[39m \u001b[33mDate\u001b[39m\u001b[33m.\u001b[39mnow() \u001b[33m-\u001b[39m startTime\n \u001b[90m 543 |\u001b[39m       \u001b[0m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:540:66\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:40:48.253Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-性能测试-大量数据渲染性能-Mobile-Chrome/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-性能测试-大量数据渲染性能-Mobile-Chrome/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 66, "line": 540}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-5251af01a41c174db63c", "file": "channel_management.spec.ts", "line": 510, "column": 9}, {"title": "页面加载性能应该符合要求", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 73, "parallelIndex": 1, "status": "passed", "duration": 937, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:41:12.755Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "672dd40e39adbe973451-315c851419d6154b92cd", "file": "channel_management.spec.ts", "line": 496, "column": 9}, {"title": "大量数据渲染性能", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 74, "parallelIndex": 2, "status": "failed", "duration": 5972, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"channel-list\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"channel-list\"]')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"channel-list\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"channel-list\"]')\u001b[22m\n\n    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:540:66", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 66, "line": 540}, "snippet": "\u001b[0m \u001b[90m 538 |\u001b[39m       \n \u001b[90m 539 |\u001b[39m       \u001b[90m// 等待列表渲染完成\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 540 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"channel-list\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 541 |\u001b[39m       \n \u001b[90m 542 |\u001b[39m       \u001b[36mconst\u001b[39m renderTime \u001b[33m=\u001b[39m \u001b[33mDate\u001b[39m\u001b[33m.\u001b[39mnow() \u001b[33m-\u001b[39m startTime\n \u001b[90m 543 |\u001b[39m       \u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 66, "line": 540}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('[data-testid=\"channel-list\"]')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('[data-testid=\"channel-list\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 538 |\u001b[39m       \n \u001b[90m 539 |\u001b[39m       \u001b[90m// 等待列表渲染完成\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 540 |\u001b[39m       \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'[data-testid=\"channel-list\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\n \u001b[90m     |\u001b[39m                                                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 541 |\u001b[39m       \n \u001b[90m 542 |\u001b[39m       \u001b[36mconst\u001b[39m renderTime \u001b[33m=\u001b[39m \u001b[33mDate\u001b[39m\u001b[33m.\u001b[39mnow() \u001b[33m-\u001b[39m startTime\n \u001b[90m 543 |\u001b[39m       \u001b[0m\n\u001b[2m    at /Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts:540:66\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-03T18:41:13.383Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-性能测试-大量数据渲染性能-Mobile-Safari/test-failed-1.png"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/test-results/channel_management-渠道管理E2E测试-性能测试-大量数据渲染性能-Mobile-Safari/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/channel_management.spec.ts", "column": 66, "line": 540}}], "status": "unexpected"}], "id": "672dd40e39adbe973451-404c3affcabcab47da29", "file": "channel_management.spec.ts", "line": 510, "column": 9}]}]}]}], "errors": [], "stats": {"startTime": "2025-08-03T18:39:00.416Z", "duration": 139095.738, "expected": 5, "skipped": 0, "unexpected": 70, "flaky": 0}}