{"config": {"configFile": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/frontend/playwright.config.ts", "rootDir": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "playwright-report"}], ["json", {"outputFile": "test-results.json"}], ["line", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "chromium", "name": "chromium", "testDir": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "firefox", "name": "firefox", "testDir": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "webkit", "name": "webkit", "testDir": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.54.2", "workers": 5, "webServer": {"command": "npm run dev", "url": "http://localhost:5173", "reuseExistingServer": true, "timeout": 120000}}, "suites": [], "errors": [{"message": "Error: Requiring @playwright/test second time, \nFirst:\nError: \n    at Object.<anonymous> (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/frontend/node_modules/playwright/lib/index.js:61:33)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object.<anonymous> (node:internal/modules/cjs/loader:1895:10)\n    at Object.newLoader2 [as .js] (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/frontend/node_modules/playwright/lib/third_party/pirates.js:52:22)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/frontend/node_modules/playwright/test.js:17:13)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object.<anonymous> (node:internal/modules/cjs/loader:1895:10)\n    at Object.newLoader2 [as .js] (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/frontend/node_modules/playwright/lib/third_party/pirates.js:52:22)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at cjsLoader (node:internal/modules/esm/translators:266:5)\n    at ModuleWrap.<anonymous> (node:internal/modules/esm/translators:200:7)\n    at ModuleJob.run (node:internal/modules/esm/module_job:274:25)\n    at onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:644:26)\n    at requireOrImport (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/frontend/node_modules/playwright/lib/transform/transform.js:214:12)\n    at loadUserConfig (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/frontend/node_modules/playwright/lib/common/configLoader.js:106:46)\n    at loadConfig (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/frontend/node_modules/playwright/lib/common/configLoader.js:118:22)\n    at loadConfigFromFile (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/frontend/node_modules/playwright/lib/common/configLoader.js:330:10)\n    at runTests (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/frontend/node_modules/playwright/lib/program.js:166:18)\n    at i.<anonymous> (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/frontend/node_modules/playwright/lib/program.js:65:7)\n\nSecond: ", "stack": "Error: Requiring @playwright/test second time, \nFirst:\nError: \n    at Object.<anonymous> (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/frontend/node_modules/playwright/lib/index.js:61:33)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object.<anonymous> (node:internal/modules/cjs/loader:1895:10)\n    at Object.newLoader2 [as .js] (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/frontend/node_modules/playwright/lib/third_party/pirates.js:52:22)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/frontend/node_modules/playwright/test.js:17:13)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object.<anonymous> (node:internal/modules/cjs/loader:1895:10)\n    at Object.newLoader2 [as .js] (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/frontend/node_modules/playwright/lib/third_party/pirates.js:52:22)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at cjsLoader (node:internal/modules/esm/translators:266:5)\n    at ModuleWrap.<anonymous> (node:internal/modules/esm/translators:200:7)\n    at ModuleJob.run (node:internal/modules/esm/module_job:274:25)\n    at onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:644:26)\n    at requireOrImport (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/frontend/node_modules/playwright/lib/transform/transform.js:214:12)\n    at loadUserConfig (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/frontend/node_modules/playwright/lib/common/configLoader.js:106:46)\n    at loadConfig (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/frontend/node_modules/playwright/lib/common/configLoader.js:118:22)\n    at loadConfigFromFile (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/frontend/node_modules/playwright/lib/common/configLoader.js:330:10)\n    at runTests (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/frontend/node_modules/playwright/lib/program.js:166:18)\n    at i.<anonymous> (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/frontend/node_modules/playwright/lib/program.js:65:7)\n\nSecond: \n    at Object.<anonymous> (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/node_modules/playwright/lib/index.js:56:11)\n    at Object.<anonymous> (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/node_modules/playwright/test.js:17:13)\n    at Object.<anonymous> (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/node_modules/@playwright/test/index.js:17:18)\n    at Object.<anonymous> (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/channel_management.spec.ts:11:1)", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/channel_management.spec.ts", "column": 1, "line": 11}, "snippet": "\u001b[90m   at \u001b[39mchannel_management.spec.ts:11\n\n\u001b[0m \u001b[90m  9 |\u001b[39m \u001b[90m */\u001b[39m\n \u001b[90m 10 |\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 11 |\u001b[39m \u001b[36mimport\u001b[39m { test\u001b[33m,\u001b[39m expect\u001b[33m,\u001b[39m \u001b[33mPage\u001b[39m } \u001b[36mfrom\u001b[39m \u001b[32m'@playwright/test'\u001b[39m\n \u001b[90m    |\u001b[39m \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 12 |\u001b[39m\n \u001b[90m 13 |\u001b[39m \u001b[90m// 测试数据\u001b[39m\n \u001b[90m 14 |\u001b[39m \u001b[36mconst\u001b[39m testData \u001b[33m=\u001b[39m {\u001b[0m"}, {"message": "Error: Requiring @playwright/test second time, \nFirst:\nError: \n    at Object.<anonymous> (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/frontend/node_modules/playwright/lib/index.js:61:33)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object.<anonymous> (node:internal/modules/cjs/loader:1895:10)\n    at Object.newLoader2 [as .js] (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/frontend/node_modules/playwright/lib/third_party/pirates.js:52:22)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/frontend/node_modules/playwright/test.js:17:13)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object.<anonymous> (node:internal/modules/cjs/loader:1895:10)\n    at Object.newLoader2 [as .js] (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/frontend/node_modules/playwright/lib/third_party/pirates.js:52:22)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at cjsLoader (node:internal/modules/esm/translators:266:5)\n    at ModuleWrap.<anonymous> (node:internal/modules/esm/translators:200:7)\n    at ModuleJob.run (node:internal/modules/esm/module_job:274:25)\n    at onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:644:26)\n    at requireOrImport (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/frontend/node_modules/playwright/lib/transform/transform.js:214:12)\n    at loadUserConfig (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/frontend/node_modules/playwright/lib/common/configLoader.js:106:46)\n    at loadConfig (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/frontend/node_modules/playwright/lib/common/configLoader.js:118:22)\n    at loadConfigFromFile (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/frontend/node_modules/playwright/lib/common/configLoader.js:330:10)\n    at runTests (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/frontend/node_modules/playwright/lib/program.js:166:18)\n    at i.<anonymous> (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/frontend/node_modules/playwright/lib/program.js:65:7)\n\nSecond: ", "stack": "Error: Requiring @playwright/test second time, \nFirst:\nError: \n    at Object.<anonymous> (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/frontend/node_modules/playwright/lib/index.js:61:33)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object.<anonymous> (node:internal/modules/cjs/loader:1895:10)\n    at Object.newLoader2 [as .js] (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/frontend/node_modules/playwright/lib/third_party/pirates.js:52:22)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/frontend/node_modules/playwright/test.js:17:13)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object.<anonymous> (node:internal/modules/cjs/loader:1895:10)\n    at Object.newLoader2 [as .js] (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/frontend/node_modules/playwright/lib/third_party/pirates.js:52:22)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at cjsLoader (node:internal/modules/esm/translators:266:5)\n    at ModuleWrap.<anonymous> (node:internal/modules/esm/translators:200:7)\n    at ModuleJob.run (node:internal/modules/esm/module_job:274:25)\n    at onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:644:26)\n    at requireOrImport (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/frontend/node_modules/playwright/lib/transform/transform.js:214:12)\n    at loadUserConfig (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/frontend/node_modules/playwright/lib/common/configLoader.js:106:46)\n    at loadConfig (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/frontend/node_modules/playwright/lib/common/configLoader.js:118:22)\n    at loadConfigFromFile (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/frontend/node_modules/playwright/lib/common/configLoader.js:330:10)\n    at runTests (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/frontend/node_modules/playwright/lib/program.js:166:18)\n    at i.<anonymous> (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/src/frontend/node_modules/playwright/lib/program.js:65:7)\n\nSecond: \n    at Object.<anonymous> (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/node_modules/playwright/lib/index.js:56:11)\n    at Object.<anonymous> (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/node_modules/playwright/test.js:17:13)\n    at Object.<anonymous> (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/node_modules/@playwright/test/index.js:17:18)\n    at Object.<anonymous> (/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/basic.spec.ts:7:1)", "location": {"file": "/Users/<USER>/Documents/Augment/chaiguanjia_ag_8.3/tests/e2e/tests/basic.spec.ts", "column": 1, "line": 7}, "snippet": "\u001b[90m   at \u001b[39mtests/basic.spec.ts:7\n\n\u001b[0m \u001b[90m  5 |\u001b[39m \u001b[90m */\u001b[39m\n \u001b[90m  6 |\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  7 |\u001b[39m \u001b[36mimport\u001b[39m { test\u001b[33m,\u001b[39m expect } \u001b[36mfrom\u001b[39m \u001b[32m'@playwright/test'\u001b[39m\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m  8 |\u001b[39m\n \u001b[90m  9 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'基础功能测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 10 |\u001b[39m   test(\u001b[32m'应用首页应该正常加载'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m"}, {"message": "Error: No tests found", "stack": "Error: No tests found"}], "stats": {"startTime": "2025-08-03T18:36:16.303Z", "duration": 262.59400000000005, "expected": 0, "skipped": 0, "unexpected": 0, "flaky": 0}}