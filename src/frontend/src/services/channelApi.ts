/**
 * 渠道管理API服务
 * 
 * 提供渠道相关的API调用功能
 */

import axios from 'axios'

// API基础配置
const api = axios.create({
  baseURL: 'http://localhost:8000/api/v1',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 添加认证token
    const token = localStorage.getItem('auth_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => response.data,
  (error) => {
    if (error.response?.status === 401) {
      // 处理认证失败
      localStorage.removeItem('auth_token')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

// 类型定义
export interface Channel {
  id: string
  platform: string
  platformAccountId: string
  displayName: string
  avatarUrl?: string
  status: 'connected' | 'disconnected' | 'error' | 'paused'
  isDeleted: boolean
  lastConnectedAt: string
  lastActiveAt: string
  createdAt: string
  updatedAt: string
}

export interface ValidationResult {
  valid: boolean
  accountInfo?: {
    id: string
    name: string
    avatar?: string
  }
  error?: string
  missingFields?: string[]
}

export interface ConnectChannelRequest {
  platform: string
  cookie: string
  accountInfo: {
    id: string
    name: string
    avatar?: string
  }
}

export interface ConnectChannelResponse {
  success: boolean
  channel: Channel
}

// 渠道管理API
export const channelApi = {
  // 获取渠道列表
  async getChannels(params?: {
    platform?: string
    status?: string
    page?: number
    limit?: number
  }): Promise<{ channels: Channel[], total: number }> {
    return api.get('/channels', { params })
  },

  // 获取单个渠道
  async getChannel(id: string): Promise<Channel> {
    return api.get(`/channels/${id}`)
  },

  // 创建渠道
  async createChannel(data: Partial<Channel>): Promise<Channel> {
    return api.post('/channels', data)
  },

  // 更新渠道
  async updateChannel(id: string, data: Partial<Channel>): Promise<Channel> {
    return api.put(`/channels/${id}`, data)
  },

  // 软删除渠道
  async deleteChannel(id: string): Promise<void> {
    return api.delete(`/channels/${id}`)
  },

  // 恢复已删除渠道
  async restoreChannel(id: string): Promise<Channel> {
    return api.post(`/channels/${id}/restore`)
  },

  // 获取已删除渠道列表
  async getDeletedChannels(): Promise<Channel[]> {
    return api.get('/channels/deleted')
  }
}

// Cookie验证API
export const validateCookie = async (
  platform: string, 
  cookie: string
): Promise<ValidationResult> => {
  try {
    const response = await api.post('/channels/validate-cookie', {
      platform,
      cookie
    })
    return response
  } catch (error: any) {
    if (error.response?.data) {
      return error.response.data
    }
    throw error
  }
}

// 连接渠道API
export const connectChannel = async (
  data: ConnectChannelRequest
): Promise<ConnectChannelResponse> => {
  try {
    const response = await api.post('/channels/connect', data)
    return response
  } catch (error: any) {
    if (error.response?.data?.code === 'DUPLICATE_CHANNEL') {
      throw {
        code: 'DUPLICATE_CHANNEL',
        message: error.response.data.message
      }
    }
    throw error
  }
}

// 重连渠道API
export const reconnectChannel = async (channelId: string): Promise<void> => {
  return api.post(`/channels/${channelId}/reconnect`)
}

// 获取渠道状态API
export const getChannelStatus = async (channelId: string): Promise<{
  status: string
  lastActiveAt: string
}> => {
  return api.get(`/channels/${channelId}/status`)
}

// 更新Cookie API
export const updateCookie = async (
  channelId: string, 
  cookie: string
): Promise<{ success: boolean }> => {
  return api.put(`/channels/${channelId}/cookie`, { cookie })
}

// 获取连接历史API
export const getConnectionLogs = async (
  channelId: string,
  params?: { page?: number, limit?: number }
): Promise<{
  logs: Array<{
    id: string
    status: string
    timestamp: string
    message: string
    errorMessage?: string
  }>
  total: number
}> => {
  return api.get(`/channels/${channelId}/logs`, { params })
}

// 获取监控统计API
export const getMonitoringStats = async (
  channelId: string,
  timeRange?: string
): Promise<{
  todayConnections: number
  averageResponseTime: number
  todayErrors: number
  connectionQuality: number
}> => {
  return api.get(`/channels/${channelId}/stats`, {
    params: { timeRange }
  })
}

// 导出监控数据API
export const exportMonitoringData = async (
  channelId: string,
  format: 'csv' | 'json' = 'csv'
): Promise<Blob> => {
  const response = await api.get(`/channels/${channelId}/export`, {
    params: { format },
    responseType: 'blob'
  })
  return response
}

// 批量操作API
export const batchOperations = {
  // 批量删除渠道
  async deleteChannels(channelIds: string[]): Promise<void> {
    return api.post('/channels/batch/delete', { channelIds })
  },

  // 批量恢复渠道
  async restoreChannels(channelIds: string[]): Promise<void> {
    return api.post('/channels/batch/restore', { channelIds })
  },

  // 批量更新状态
  async updateStatus(channelIds: string[], status: string): Promise<void> {
    return api.post('/channels/batch/status', { channelIds, status })
  }
}

export default api
