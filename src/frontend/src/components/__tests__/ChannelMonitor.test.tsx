/**
 * ChannelMonitor组件测试
 * 
 * 基于BDD剧本测试状态监控组件的核心功能：
 * - 实时状态显示
 * - 重连机制
 * - Cookie失效检测
 * - 监控数据统计
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { ChannelMonitor } from '../ChannelMonitor'

// Mock WebSocket
const mockWebSocket = {
  send: vi.fn(),
  close: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  readyState: WebSocket.OPEN
}

global.WebSocket = vi.fn(() => mockWebSocket) as any

// Mock API调用
const mockGetChannelStatus = vi.fn()
const mockReconnectChannel = vi.fn()
const mockUpdateCookie = vi.fn()

vi.mock('../../services/channelApi', () => ({
  getChannelStatus: mockGetChannelStatus,
  reconnectChannel: mockReconnectChannel,
  updateCookie: mockUpdateCookie
}))

// Mock数据
const mockChannel = {
  id: '1',
  platform: 'xianyu',
  platformAccountId: 'xianyu_shop_123',
  displayName: '主店铺',
  status: 'connected',
  lastConnectedAt: '2025-08-01T10:30:00Z',
  lastActiveAt: '2025-08-01T15:45:00Z',
  connectionLogs: [
    {
      id: '1',
      status: 'connected',
      timestamp: '2025-08-01T15:45:00Z',
      message: '连接成功'
    },
    {
      id: '2',
      status: 'disconnected',
      timestamp: '2025-08-01T14:30:00Z',
      message: '连接断开'
    }
  ]
}

// Mock props
const defaultProps = {
  channel: mockChannel,
  onReconnect: vi.fn(),
  onUpdateCookie: vi.fn(),
  onStatusChange: vi.fn()
}

describe('ChannelMonitor组件', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    vi.useFakeTimers()
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  describe('基础渲染功能', () => {
    it('应该正确渲染监控界面', () => {
      render(<ChannelMonitor {...defaultProps} />)
      
      expect(screen.getByText('渠道监控')).toBeInTheDocument()
      expect(screen.getByText('主店铺')).toBeInTheDocument()
      expect(screen.getByText('闲鱼')).toBeInTheDocument()
      expect(screen.getByTestId('channel-status-indicator')).toBeInTheDocument()
    })

    it('应该显示当前连接状态', () => {
      render(<ChannelMonitor {...defaultProps} />)
      
      expect(screen.getByText('已连接')).toBeInTheDocument()
      expect(screen.getByTestId('status-connected')).toBeInTheDocument()
    })

    it('应该显示最后活跃时间', () => {
      render(<ChannelMonitor {...defaultProps} />)
      
      expect(screen.getByText('最后活跃')).toBeInTheDocument()
      expect(screen.getByText('2025-08-01 15:45')).toBeInTheDocument()
    })

    it('应该显示连接历史', () => {
      render(<ChannelMonitor {...defaultProps} />)
      
      expect(screen.getByText('连接历史')).toBeInTheDocument()
      expect(screen.getByText('连接成功')).toBeInTheDocument()
      expect(screen.getByText('连接断开')).toBeInTheDocument()
    })
  })

  describe('实时状态监控', () => {
    it('应该建立WebSocket连接', () => {
      render(<ChannelMonitor {...defaultProps} />)
      
      expect(global.WebSocket).toHaveBeenCalledWith(
        expect.stringContaining('/ws/channel/1/monitor')
      )
    })

    it('应该处理状态变更消息', async () => {
      render(<ChannelMonitor {...defaultProps} />)
      
      // 模拟WebSocket消息
      const statusMessage = {
        type: 'status_change',
        channelId: '1',
        status: 'disconnected',
        timestamp: '2025-08-01T16:00:00Z',
        message: '连接断开'
      }
      
      // 触发WebSocket消息事件
      const messageHandler = mockWebSocket.addEventListener.mock.calls
        .find(call => call[0] === 'message')?.[1]
      
      messageHandler?.({ data: JSON.stringify(statusMessage) })
      
      await waitFor(() => {
        expect(defaultProps.onStatusChange).toHaveBeenCalledWith(statusMessage)
      })
    })

    it('应该实时更新状态显示', async () => {
      const { rerender } = render(<ChannelMonitor {...defaultProps} />)
      
      // 更新渠道状态
      const updatedChannel = {
        ...mockChannel,
        status: 'disconnected' as const
      }
      
      rerender(<ChannelMonitor {...defaultProps} channel={updatedChannel} />)
      
      expect(screen.getByText('已断开')).toBeInTheDocument()
      expect(screen.getByTestId('status-disconnected')).toBeInTheDocument()
    })

    it('应该显示连接质量指标', () => {
      render(<ChannelMonitor {...defaultProps} />)
      
      expect(screen.getByText('连接质量')).toBeInTheDocument()
      expect(screen.getByTestId('connection-quality')).toBeInTheDocument()
    })

    it('应该定期检查连接状态', async () => {
      mockGetChannelStatus.mockResolvedValue({
        status: 'connected',
        lastActiveAt: '2025-08-01T16:00:00Z'
      })
      
      render(<ChannelMonitor {...defaultProps} />)
      
      // 快进30秒（状态检查间隔）
      vi.advanceTimersByTime(30000)
      
      await waitFor(() => {
        expect(mockGetChannelStatus).toHaveBeenCalledWith('1')
      })
    })
  })

  describe('重连机制', () => {
    it('应该支持手动重连', async () => {
      const user = userEvent.setup()
      mockReconnectChannel.mockResolvedValue({ success: true })
      
      render(<ChannelMonitor {...defaultProps} />)
      
      const reconnectButton = screen.getByText('重新连接')
      await user.click(reconnectButton)
      
      expect(mockReconnectChannel).toHaveBeenCalledWith('1')
      expect(defaultProps.onReconnect).toHaveBeenCalledWith('1')
    })

    it('应该显示重连进度', async () => {
      const user = userEvent.setup()
      mockReconnectChannel.mockImplementation(() => 
        new Promise(resolve => setTimeout(resolve, 1000))
      )
      
      render(<ChannelMonitor {...defaultProps} />)
      
      const reconnectButton = screen.getByText('重新连接')
      await user.click(reconnectButton)
      
      expect(screen.getByText('重连中...')).toBeInTheDocument()
      expect(screen.getByTestId('reconnect-spinner')).toBeInTheDocument()
    })

    it('应该处理重连失败', async () => {
      const user = userEvent.setup()
      mockReconnectChannel.mockRejectedValue(new Error('重连失败'))
      
      render(<ChannelMonitor {...defaultProps} />)
      
      const reconnectButton = screen.getByText('重新连接')
      await user.click(reconnectButton)
      
      await waitFor(() => {
        expect(screen.getByText('重连失败，请检查网络连接')).toBeInTheDocument()
      })
    })

    it('应该支持自动重连', async () => {
      const disconnectedChannel = {
        ...mockChannel,
        status: 'disconnected' as const
      }
      
      render(<ChannelMonitor {...defaultProps} channel={disconnectedChannel} />)
      
      // 启用自动重连
      const autoReconnectSwitch = screen.getByTestId('auto-reconnect-switch')
      await userEvent.click(autoReconnectSwitch)
      
      // 快进重连间隔时间
      vi.advanceTimersByTime(60000)
      
      await waitFor(() => {
        expect(mockReconnectChannel).toHaveBeenCalledWith('1')
      })
    })
  })

  describe('Cookie失效处理', () => {
    it('应该检测Cookie失效', async () => {
      render(<ChannelMonitor {...defaultProps} />)
      
      // 模拟Cookie失效消息
      const cookieExpiredMessage = {
        type: 'cookie_expired',
        channelId: '1',
        timestamp: '2025-08-01T16:00:00Z',
        message: 'Cookie已过期'
      }
      
      const messageHandler = mockWebSocket.addEventListener.mock.calls
        .find(call => call[0] === 'message')?.[1]
      
      messageHandler?.({ data: JSON.stringify(cookieExpiredMessage) })
      
      await waitFor(() => {
        expect(screen.getByText('Cookie已过期')).toBeInTheDocument()
        expect(screen.getByText('更新Cookie')).toBeInTheDocument()
      })
    })

    it('应该支持更新Cookie', async () => {
      const user = userEvent.setup()
      mockUpdateCookie.mockResolvedValue({ success: true })
      
      // 渲染Cookie过期状态
      const expiredChannel = {
        ...mockChannel,
        status: 'cookie_expired' as const
      }
      
      render(<ChannelMonitor {...defaultProps} channel={expiredChannel} />)
      
      const updateCookieButton = screen.getByText('更新Cookie')
      await user.click(updateCookieButton)
      
      expect(defaultProps.onUpdateCookie).toHaveBeenCalledWith('1')
    })

    it('应该显示Cookie状态', () => {
      const expiredChannel = {
        ...mockChannel,
        status: 'cookie_expired' as const
      }
      
      render(<ChannelMonitor {...defaultProps} channel={expiredChannel} />)
      
      expect(screen.getByText('Cookie已过期')).toBeInTheDocument()
      expect(screen.getByTestId('cookie-status-expired')).toBeInTheDocument()
    })
  })

  describe('监控数据统计', () => {
    it('应该显示连接统计', () => {
      render(<ChannelMonitor {...defaultProps} />)
      
      expect(screen.getByText('连接统计')).toBeInTheDocument()
      expect(screen.getByText('今日连接次数')).toBeInTheDocument()
      expect(screen.getByText('平均响应时间')).toBeInTheDocument()
    })

    it('应该显示错误统计', () => {
      render(<ChannelMonitor {...defaultProps} />)
      
      expect(screen.getByText('错误统计')).toBeInTheDocument()
      expect(screen.getByText('今日错误次数')).toBeInTheDocument()
    })

    it('应该支持导出监控数据', async () => {
      const user = userEvent.setup()
      render(<ChannelMonitor {...defaultProps} />)
      
      const exportButton = screen.getByText('导出数据')
      await user.click(exportButton)
      
      // 验证导出功能被调用
      expect(screen.getByText('数据导出中...')).toBeInTheDocument()
    })
  })

  describe('错误处理', () => {
    it('应该处理WebSocket连接失败', () => {
      const errorHandler = mockWebSocket.addEventListener.mock.calls
        .find(call => call[0] === 'error')?.[1]
      
      render(<ChannelMonitor {...defaultProps} />)
      
      errorHandler?.({ error: 'Connection failed' })
      
      expect(screen.getByText('监控连接异常')).toBeInTheDocument()
    })

    it('应该处理无效的监控数据', () => {
      const invalidChannel = {
        ...mockChannel,
        connectionLogs: null
      }
      
      render(<ChannelMonitor {...defaultProps} channel={invalidChannel} />)
      
      // 验证组件不会崩溃
      expect(screen.getByTestId('channel-monitor-container')).toBeInTheDocument()
    })

    it('应该显示网络错误提示', () => {
      const offlineChannel = {
        ...mockChannel,
        status: 'error' as const
      }
      
      render(<ChannelMonitor {...defaultProps} channel={offlineChannel} />)
      
      expect(screen.getByText('连接异常')).toBeInTheDocument()
      expect(screen.getByText('检查网络连接')).toBeInTheDocument()
    })
  })

  describe('性能优化', () => {
    it('应该在组件卸载时清理WebSocket连接', () => {
      const { unmount } = render(<ChannelMonitor {...defaultProps} />)
      
      unmount()
      
      expect(mockWebSocket.close).toHaveBeenCalled()
    })

    it('应该防抖处理频繁的状态更新', async () => {
      render(<ChannelMonitor {...defaultProps} />)
      
      const messageHandler = mockWebSocket.addEventListener.mock.calls
        .find(call => call[0] === 'message')?.[1]
      
      // 快速发送多个状态更新
      for (let i = 0; i < 5; i++) {
        messageHandler?.({ 
          data: JSON.stringify({
            type: 'status_change',
            channelId: '1',
            status: 'connected'
          })
        })
      }
      
      // 验证只处理了最后一次更新
      await waitFor(() => {
        expect(defaultProps.onStatusChange).toHaveBeenCalledTimes(1)
      })
    })
  })
})
