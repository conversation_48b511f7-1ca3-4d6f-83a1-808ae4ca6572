/**
 * ChannelConnection组件测试
 * 
 * 基于BDD剧本测试渠道连接对话框的核心功能：
 * - Cookie输入和验证
 * - 连接流程处理
 * - 错误处理和重试
 * - 安全性验证
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { ChannelConnection } from '../ChannelConnection'

// Mock API调用
const mockValidateCookie = vi.fn()
const mockConnectChannel = vi.fn()

vi.mock('../../services/channelApi', () => ({
  validateCookie: mockValidateCookie,
  connectChannel: mockConnectChannel
}))

// Mock props
const defaultProps = {
  isOpen: true,
  platform: 'xianyu',
  onClose: vi.fn(),
  onSuccess: vi.fn(),
  onError: vi.fn()
}

describe('ChannelConnection组件', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('基础渲染功能', () => {
    it('应该正确渲染连接对话框', () => {
      render(<ChannelConnection {...defaultProps} />)
      
      expect(screen.getByText('连接闲鱼账号')).toBeInTheDocument()
      expect(screen.getByText('请输入您的闲鱼Cookie信息')).toBeInTheDocument()
      expect(screen.getByTestId('cookie-input')).toBeInTheDocument()
      expect(screen.getByText('验证Cookie')).toBeInTheDocument()
    })

    it('应该在关闭状态下不渲染', () => {
      render(<ChannelConnection {...defaultProps} isOpen={false} />)
      
      expect(screen.queryByText('连接闲鱼账号')).not.toBeInTheDocument()
    })

    it('应该根据平台显示正确的标题', () => {
      render(<ChannelConnection {...defaultProps} platform="douyin" />)
      
      expect(screen.getByText('连接抖音账号')).toBeInTheDocument()
    })
  })

  describe('Cookie输入功能', () => {
    it('应该支持手动输入Cookie', async () => {
      const user = userEvent.setup()
      render(<ChannelConnection {...defaultProps} />)
      
      const cookieInput = screen.getByTestId('cookie-input')
      const testCookie = 'test_cookie_value=123; session_id=abc'
      
      await user.type(cookieInput, testCookie)
      
      expect(cookieInput).toHaveValue(testCookie)
    })

    it('应该显示Cookie输入提示', () => {
      render(<ChannelConnection {...defaultProps} />)
      
      expect(screen.getByText('请粘贴完整的Cookie字符串')).toBeInTheDocument()
      expect(screen.getByText('如何获取Cookie？')).toBeInTheDocument()
    })

    it('应该支持清空Cookie输入', async () => {
      const user = userEvent.setup()
      render(<ChannelConnection {...defaultProps} />)
      
      const cookieInput = screen.getByTestId('cookie-input')
      await user.type(cookieInput, 'test_cookie')
      
      const clearButton = screen.getByTestId('clear-cookie-button')
      await user.click(clearButton)
      
      expect(cookieInput).toHaveValue('')
    })

    it('应该验证Cookie格式', async () => {
      const user = userEvent.setup()
      render(<ChannelConnection {...defaultProps} />)
      
      const cookieInput = screen.getByTestId('cookie-input')
      await user.type(cookieInput, 'invalid_cookie_format')
      
      const validateButton = screen.getByText('验证Cookie')
      await user.click(validateButton)
      
      expect(screen.getByText('Cookie格式不正确')).toBeInTheDocument()
    })
  })

  describe('Cookie验证功能', () => {
    it('应该成功验证有效的Cookie', async () => {
      const user = userEvent.setup()
      mockValidateCookie.mockResolvedValue({
        valid: true,
        accountInfo: {
          id: 'xianyu_123',
          name: '测试店铺',
          avatar: 'https://example.com/avatar.jpg'
        }
      })
      
      render(<ChannelConnection {...defaultProps} />)
      
      const cookieInput = screen.getByTestId('cookie-input')
      await user.type(cookieInput, 'valid_cookie=123; session=abc')
      
      const validateButton = screen.getByText('验证Cookie')
      await user.click(validateButton)
      
      await waitFor(() => {
        expect(screen.getByText('Cookie验证成功')).toBeInTheDocument()
        expect(screen.getByText('测试店铺')).toBeInTheDocument()
        expect(screen.getByText('连接账号')).toBeInTheDocument()
      })
    })

    it('应该处理Cookie验证失败', async () => {
      const user = userEvent.setup()
      mockValidateCookie.mockResolvedValue({
        valid: false,
        error: 'Cookie已过期'
      })
      
      render(<ChannelConnection {...defaultProps} />)
      
      const cookieInput = screen.getByTestId('cookie-input')
      await user.type(cookieInput, 'expired_cookie=123')
      
      const validateButton = screen.getByText('验证Cookie')
      await user.click(validateButton)
      
      await waitFor(() => {
        expect(screen.getByText('Cookie已过期')).toBeInTheDocument()
        expect(screen.getByText('重新验证')).toBeInTheDocument()
      })
    })

    it('应该显示验证加载状态', async () => {
      const user = userEvent.setup()
      mockValidateCookie.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 1000)))
      
      render(<ChannelConnection {...defaultProps} />)
      
      const cookieInput = screen.getByTestId('cookie-input')
      await user.type(cookieInput, 'test_cookie=123')
      
      const validateButton = screen.getByText('验证Cookie')
      await user.click(validateButton)
      
      expect(screen.getByText('验证中...')).toBeInTheDocument()
      expect(screen.getByTestId('validation-spinner')).toBeInTheDocument()
    })

    it('应该处理网络错误', async () => {
      const user = userEvent.setup()
      mockValidateCookie.mockRejectedValue(new Error('网络连接失败'))
      
      render(<ChannelConnection {...defaultProps} />)
      
      const cookieInput = screen.getByTestId('cookie-input')
      await user.type(cookieInput, 'test_cookie=123')
      
      const validateButton = screen.getByText('验证Cookie')
      await user.click(validateButton)
      
      await waitFor(() => {
        expect(screen.getByText('网络连接异常，请检查网络后重试')).toBeInTheDocument()
        expect(screen.getByText('重试')).toBeInTheDocument()
      })
    })
  })

  describe('连接流程功能', () => {
    it('应该成功连接账号', async () => {
      const user = userEvent.setup()
      
      // Mock验证成功
      mockValidateCookie.mockResolvedValue({
        valid: true,
        accountInfo: {
          id: 'xianyu_123',
          name: '测试店铺',
          avatar: 'https://example.com/avatar.jpg'
        }
      })
      
      // Mock连接成功
      mockConnectChannel.mockResolvedValue({
        success: true,
        channel: {
          id: 'channel_123',
          platform: 'xianyu',
          displayName: '测试店铺'
        }
      })
      
      render(<ChannelConnection {...defaultProps} />)
      
      // 输入并验证Cookie
      const cookieInput = screen.getByTestId('cookie-input')
      await user.type(cookieInput, 'valid_cookie=123')
      
      const validateButton = screen.getByText('验证Cookie')
      await user.click(validateButton)
      
      await waitFor(() => {
        expect(screen.getByText('连接账号')).toBeInTheDocument()
      })
      
      // 连接账号
      const connectButton = screen.getByText('连接账号')
      await user.click(connectButton)
      
      await waitFor(() => {
        expect(defaultProps.onSuccess).toHaveBeenCalledWith({
          id: 'channel_123',
          platform: 'xianyu',
          displayName: '测试店铺'
        })
      })
    })

    it('应该处理连接失败', async () => {
      const user = userEvent.setup()
      
      mockValidateCookie.mockResolvedValue({
        valid: true,
        accountInfo: { id: 'xianyu_123', name: '测试店铺' }
      })
      
      mockConnectChannel.mockRejectedValue(new Error('连接失败'))
      
      render(<ChannelConnection {...defaultProps} />)
      
      const cookieInput = screen.getByTestId('cookie-input')
      await user.type(cookieInput, 'valid_cookie=123')
      
      const validateButton = screen.getByText('验证Cookie')
      await user.click(validateButton)
      
      await waitFor(() => {
        const connectButton = screen.getByText('连接账号')
        user.click(connectButton)
      })
      
      await waitFor(() => {
        expect(defaultProps.onError).toHaveBeenCalledWith(expect.any(Error))
      })
    })

    it('应该处理重复连接', async () => {
      const user = userEvent.setup()
      
      mockValidateCookie.mockResolvedValue({
        valid: true,
        accountInfo: { id: 'xianyu_123', name: '测试店铺' }
      })
      
      mockConnectChannel.mockRejectedValue({
        code: 'DUPLICATE_CHANNEL',
        message: '该账号已存在'
      })
      
      render(<ChannelConnection {...defaultProps} />)
      
      const cookieInput = screen.getByTestId('cookie-input')
      await user.type(cookieInput, 'valid_cookie=123')
      
      const validateButton = screen.getByText('验证Cookie')
      await user.click(validateButton)
      
      await waitFor(() => {
        const connectButton = screen.getByText('连接账号')
        user.click(connectButton)
      })
      
      await waitFor(() => {
        expect(screen.getByText('该账号已存在')).toBeInTheDocument()
        expect(screen.getByText('查看现有账号')).toBeInTheDocument()
      })
    })
  })

  describe('安全性功能', () => {
    it('应该脱敏显示Cookie信息', async () => {
      const user = userEvent.setup()
      render(<ChannelConnection {...defaultProps} />)
      
      const cookieInput = screen.getByTestId('cookie-input')
      const longCookie = 'very_long_cookie_value_that_should_be_masked=123456789'
      
      await user.type(cookieInput, longCookie)
      
      // 失焦后应该显示脱敏版本
      await user.tab()
      
      expect(screen.getByDisplayValue(/very_long_c\*+789/)).toBeInTheDocument()
    })

    it('应该清理敏感信息', () => {
      const { unmount } = render(<ChannelConnection {...defaultProps} />)
      
      // 组件卸载时应该清理敏感信息
      unmount()
      
      // 验证localStorage中没有敏感信息
      expect(localStorage.getItem('temp_cookie')).toBeNull()
    })

    it('应该验证Cookie权限', async () => {
      const user = userEvent.setup()
      mockValidateCookie.mockResolvedValue({
        valid: false,
        error: 'Cookie权限不足',
        missingFields: ['session_id', 'user_token']
      })
      
      render(<ChannelConnection {...defaultProps} />)
      
      const cookieInput = screen.getByTestId('cookie-input')
      await user.type(cookieInput, 'insufficient_cookie=123')
      
      const validateButton = screen.getByText('验证Cookie')
      await user.click(validateButton)
      
      await waitFor(() => {
        expect(screen.getByText('Cookie权限不足')).toBeInTheDocument()
        expect(screen.getByText('缺少必要字段: session_id, user_token')).toBeInTheDocument()
        expect(screen.getByText('查看Cookie获取指南')).toBeInTheDocument()
      })
    })
  })

  describe('用户体验功能', () => {
    it('应该支持键盘快捷键', async () => {
      const user = userEvent.setup()
      render(<ChannelConnection {...defaultProps} />)
      
      // ESC键关闭对话框
      await user.keyboard('{Escape}')
      expect(defaultProps.onClose).toHaveBeenCalled()
    })

    it('应该支持粘贴Cookie', async () => {
      const user = userEvent.setup()
      render(<ChannelConnection {...defaultProps} />)
      
      const cookieInput = screen.getByTestId('cookie-input')
      await user.click(cookieInput)
      
      // 模拟粘贴操作
      const clipboardData = 'pasted_cookie=123; session=abc'
      await user.paste(clipboardData)
      
      expect(cookieInput).toHaveValue(clipboardData)
    })

    it('应该显示连接进度', async () => {
      const user = userEvent.setup()
      mockConnectChannel.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 1000)))
      
      render(<ChannelConnection {...defaultProps} />)
      
      // 先验证Cookie
      mockValidateCookie.mockResolvedValue({
        valid: true,
        accountInfo: { id: 'test', name: 'test' }
      })
      
      const cookieInput = screen.getByTestId('cookie-input')
      await user.type(cookieInput, 'valid_cookie=123')
      
      const validateButton = screen.getByText('验证Cookie')
      await user.click(validateButton)
      
      await waitFor(() => {
        const connectButton = screen.getByText('连接账号')
        user.click(connectButton)
      })
      
      expect(screen.getByText('连接中...')).toBeInTheDocument()
      expect(screen.getByTestId('connection-progress')).toBeInTheDocument()
    })
  })
})
