/**
 * ChannelList组件测试
 * 
 * 基于BDD剧本测试渠道列表组件的核心功能：
 * - 渠道列表渲染
 * - 排序功能
 * - 筛选功能
 * - 状态显示
 * - 软删除处理
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { ChannelList } from '../ChannelList'

// Mock数据
const mockChannels = [
  {
    id: '1',
    platform: 'xianyu',
    platformAccountId: 'xianyu_shop_123',
    displayName: '主店铺',
    avatarUrl: 'https://example.com/avatar1.jpg',
    status: 'connected',
    isDeleted: false,
    lastConnectedAt: '2025-08-01T10:30:00Z',
    lastActiveAt: '2025-08-01T15:45:00Z',
    createdAt: '2025-08-01T10:30:00Z',
    updatedAt: '2025-08-01T15:45:00Z'
  },
  {
    id: '2',
    platform: 'xianyu',
    platformAccountId: 'xianyu_shop_456',
    displayName: '客服店铺',
    avatarUrl: 'https://example.com/avatar2.jpg',
    status: 'disconnected',
    isDeleted: false,
    lastConnectedAt: '2025-08-02T14:20:00Z',
    lastActiveAt: '2025-08-02T16:30:00Z',
    createdAt: '2025-08-02T14:20:00Z',
    updatedAt: '2025-08-02T16:30:00Z'
  },
  {
    id: '3',
    platform: 'douyin',
    platformAccountId: 'dy_789',
    displayName: '官方号',
    avatarUrl: 'https://example.com/avatar3.jpg',
    status: 'error',
    isDeleted: false,
    lastConnectedAt: '2025-08-03T09:15:00Z',
    lastActiveAt: '2025-08-03T09:15:00Z',
    createdAt: '2025-08-03T09:15:00Z',
    updatedAt: '2025-08-03T09:15:00Z'
  }
]

// Mock props
const defaultProps = {
  channels: mockChannels,
  loading: false,
  onEdit: vi.fn(),
  onDelete: vi.fn(),
  onConnect: vi.fn(),
  onSort: vi.fn(),
  onFilter: vi.fn()
}

describe('ChannelList组件', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('基础渲染功能', () => {
    it('应该正确渲染渠道列表', () => {
      render(<ChannelList {...defaultProps} />)
      
      // 验证所有渠道都被渲染
      expect(screen.getByText('主店铺')).toBeInTheDocument()
      expect(screen.getByText('客服店铺')).toBeInTheDocument()
      expect(screen.getByText('官方号')).toBeInTheDocument()
      
      // 验证平台信息显示
      expect(screen.getAllByText('闲鱼')).toHaveLength(2)
      expect(screen.getByText('抖音')).toBeInTheDocument()
    })

    it('应该正确显示渠道状态', () => {
      render(<ChannelList {...defaultProps} />)
      
      // 验证状态显示
      expect(screen.getByText('已连接')).toBeInTheDocument()
      expect(screen.getByText('已断开')).toBeInTheDocument()
      expect(screen.getByText('连接异常')).toBeInTheDocument()
    })

    it('应该显示加载状态', () => {
      render(<ChannelList {...defaultProps} loading={true} />)
      
      expect(screen.getByTestId('channel-list-loading')).toBeInTheDocument()
    })

    it('应该显示空状态', () => {
      render(<ChannelList {...defaultProps} channels={[]} />)
      
      expect(screen.getByText('暂无渠道')).toBeInTheDocument()
      expect(screen.getByText('点击"添加渠道"开始连接您的第一个平台账号')).toBeInTheDocument()
    })
  })

  describe('排序功能', () => {
    it('应该支持按创建时间排序', async () => {
      const user = userEvent.setup()
      render(<ChannelList {...defaultProps} />)
      
      // 点击排序按钮
      const sortButton = screen.getByTestId('sort-button')
      await user.click(sortButton)
      
      // 选择按创建时间排序
      const sortByCreatedAt = screen.getByText('创建时间')
      await user.click(sortByCreatedAt)
      
      expect(defaultProps.onSort).toHaveBeenCalledWith('createdAt', 'desc')
    })

    it('应该支持按最后活跃时间排序', async () => {
      const user = userEvent.setup()
      render(<ChannelList {...defaultProps} />)
      
      const sortButton = screen.getByTestId('sort-button')
      await user.click(sortButton)
      
      const sortByLastActive = screen.getByText('最后活跃')
      await user.click(sortByLastActive)
      
      expect(defaultProps.onSort).toHaveBeenCalledWith('lastActiveAt', 'desc')
    })

    it('应该支持按状态排序', async () => {
      const user = userEvent.setup()
      render(<ChannelList {...defaultProps} />)

      const sortButton = screen.getByTestId('sort-button')
      await user.click(sortButton)

      const sortByStatus = screen.getByText('连接状态')
      await user.click(sortByStatus)

      expect(defaultProps.onSort).toHaveBeenCalledWith('status', 'desc')
    })
  })

  describe('筛选功能', () => {
    it('应该支持按平台筛选', async () => {
      const user = userEvent.setup()
      render(<ChannelList {...defaultProps} />)
      
      // 点击筛选按钮
      const filterButton = screen.getByTestId('filter-button')
      await user.click(filterButton)
      
      // 选择闲鱼平台
      const xianyuFilter = screen.getByLabelText('闲鱼')
      await user.click(xianyuFilter)
      
      expect(defaultProps.onFilter).toHaveBeenCalledWith({ platform: ['xianyu'] })
    })

    it('应该支持按状态筛选', async () => {
      const user = userEvent.setup()
      render(<ChannelList {...defaultProps} />)
      
      const filterButton = screen.getByTestId('filter-button')
      await user.click(filterButton)
      
      // 选择已连接状态
      const connectedFilter = screen.getByLabelText('已连接')
      await user.click(connectedFilter)
      
      expect(defaultProps.onFilter).toHaveBeenCalledWith({ status: ['connected'] })
    })

    it('应该支持多条件筛选', async () => {
      const user = userEvent.setup()
      render(<ChannelList {...defaultProps} />)

      const filterButton = screen.getByTestId('filter-button')
      await user.click(filterButton)

      // 选择平台
      const xianyuFilter = screen.getByLabelText('闲鱼')
      await user.click(xianyuFilter)

      expect(defaultProps.onFilter).toHaveBeenCalledWith({
        platform: ['xianyu']
      })

      // 选择状态
      const connectedFilter = screen.getByLabelText('已连接')
      await user.click(connectedFilter)

      expect(defaultProps.onFilter).toHaveBeenCalledWith({
        platform: ['xianyu'],
        status: ['connected']
      })
    })

    it('应该支持清除筛选条件', async () => {
      const user = userEvent.setup()
      render(<ChannelList {...defaultProps} />)
      
      const filterButton = screen.getByTestId('filter-button')
      await user.click(filterButton)
      
      // 点击清除按钮
      const clearButton = screen.getByText('清除筛选')
      await user.click(clearButton)
      
      expect(defaultProps.onFilter).toHaveBeenCalledWith({})
    })
  })

  describe('操作功能', () => {
    it('应该支持编辑渠道', async () => {
      const user = userEvent.setup()
      render(<ChannelList {...defaultProps} />)
      
      // 点击第一个渠道的编辑按钮
      const editButtons = screen.getAllByTestId('edit-channel-button')
      await user.click(editButtons[0])
      
      expect(defaultProps.onEdit).toHaveBeenCalledWith(mockChannels[0])
    })

    it('应该支持删除渠道', async () => {
      const user = userEvent.setup()
      render(<ChannelList {...defaultProps} />)
      
      // 点击第一个渠道的删除按钮
      const deleteButtons = screen.getAllByTestId('delete-channel-button')
      await user.click(deleteButtons[0])
      
      expect(defaultProps.onDelete).toHaveBeenCalledWith(mockChannels[0])
    })

    it('应该支持重新连接渠道', async () => {
      const user = userEvent.setup()
      render(<ChannelList {...defaultProps} />)
      
      // 点击断开连接渠道的重连按钮
      const reconnectButtons = screen.getAllByTestId('reconnect-channel-button')
      await user.click(reconnectButtons[0])
      
      expect(defaultProps.onConnect).toHaveBeenCalledWith(mockChannels[1])
    })
  })

  describe('软删除处理', () => {
    it('不应该显示已删除的渠道', () => {
      const channelsWithDeleted = [
        ...mockChannels,
        {
          id: '4',
          platform: 'xianyu',
          platformAccountId: 'deleted_shop',
          displayName: '已删除店铺',
          avatarUrl: '',
          status: 'disconnected',
          isDeleted: true,
          lastConnectedAt: '2025-08-01T10:30:00Z',
          lastActiveAt: '2025-08-01T10:30:00Z',
          createdAt: '2025-08-01T10:30:00Z',
          updatedAt: '2025-08-01T10:30:00Z'
        }
      ]
      
      render(<ChannelList {...defaultProps} channels={channelsWithDeleted} />)
      
      // 验证已删除的渠道不显示
      expect(screen.queryByText('已删除店铺')).not.toBeInTheDocument()
      
      // 验证其他渠道正常显示
      expect(screen.getByText('主店铺')).toBeInTheDocument()
      expect(screen.getByText('客服店铺')).toBeInTheDocument()
    })
  })

  describe('响应式设计', () => {
    it('应该在移动端正确显示', () => {
      // 模拟移动端视口
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      })
      
      render(<ChannelList {...defaultProps} />)
      
      // 验证移动端布局
      const listContainer = screen.getByTestId('channel-list-container')
      expect(listContainer).toHaveClass('mobile-layout')
    })
  })

  describe('错误处理', () => {
    it('应该处理无效的渠道数据', () => {
      const invalidChannels = [
        {
          id: '1',
          platform: 'unknown' as any,
          platformAccountId: '',
          displayName: '',
          status: 'unknown' as any,
          isDeleted: false,
          lastConnectedAt: '',
          lastActiveAt: '',
          createdAt: '',
          updatedAt: ''
        }
      ]

      render(<ChannelList {...defaultProps} channels={invalidChannels} />)

      // 验证组件不会崩溃
      expect(screen.getByTestId('channel-list-container')).toBeInTheDocument()
    })
  })
})
