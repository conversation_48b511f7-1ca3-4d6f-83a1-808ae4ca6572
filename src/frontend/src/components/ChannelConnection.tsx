/**
 * ChannelConnection组件
 * 
 * 渠道连接对话框，支持：
 * - Cookie输入和验证
 * - 连接流程处理
 * - 错误处理和重试
 * - 安全性保护
 */

import React, { useState, useEffect } from 'react'
import {
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  Button,
  Textarea,
  Card,
  CardBody,
  Avatar,
  Chip,
  Spinner,
  Link
} from '@heroui/react'
import { validateCookie, connectChannel } from '../services/channelApi'

// 类型定义
export interface ChannelConnectionProps {
  isOpen: boolean
  platform: string
  onClose: () => void
  onSuccess: (channel: any) => void
  onError: (error: Error) => void
}

interface AccountInfo {
  id: string
  name: string
  avatar?: string
}

interface ValidationResult {
  valid: boolean
  accountInfo?: AccountInfo
  error?: string
  missingFields?: string[]
}

// 平台映射
const platformMap = {
  xianyu: '闲鱼',
  douyin: '抖音',
  xiaohongshu: '小红书'
}

export const ChannelConnection: React.FC<ChannelConnectionProps> = ({
  isOpen,
  platform,
  onClose,
  onSuccess,
  onError
}) => {
  const [cookieValue, setCookieValue] = useState('')
  const [isValidating, setIsValidating] = useState(false)
  const [isConnecting, setIsConnecting] = useState(false)
  const [validationResult, setValidationResult] = useState<ValidationResult | null>(null)
  const [error, setError] = useState<string>('')
  const [showMasked, setShowMasked] = useState(false)

  // 重置状态
  const resetState = () => {
    setCookieValue('')
    setValidationResult(null)
    setError('')
    setIsValidating(false)
    setIsConnecting(false)
    setShowMasked(false)
  }

  // 组件卸载时清理敏感信息
  useEffect(() => {
    return () => {
      localStorage.removeItem('temp_cookie')
    }
  }, [])

  // 关闭对话框
  const handleClose = () => {
    resetState()
    onClose()
  }

  // 验证Cookie格式
  const validateCookieFormat = (cookie: string): boolean => {
    // 基本格式验证：应该包含键值对
    const cookiePattern = /^[^=]+=.+/
    return cookiePattern.test(cookie.trim())
  }

  // 脱敏显示Cookie
  const maskCookie = (cookie: string): string => {
    if (cookie.length <= 20) {
      return '*'.repeat(cookie.length)
    }
    return cookie.slice(0, 10) + '*'.repeat(cookie.length - 20) + cookie.slice(-10)
  }

  // 处理Cookie输入
  const handleCookieChange = (value: string) => {
    setCookieValue(value)
    setError('')
    setValidationResult(null)
  }

  // 处理Cookie失焦
  const handleCookieBlur = () => {
    if (cookieValue && !showMasked) {
      setShowMasked(true)
    }
  }

  // 处理Cookie聚焦
  const handleCookieFocus = () => {
    setShowMasked(false)
  }

  // 清空Cookie
  const clearCookie = () => {
    setCookieValue('')
    setShowMasked(false)
    setValidationResult(null)
    setError('')
  }

  // 验证Cookie
  const handleValidateCookie = async () => {
    if (!cookieValue.trim()) {
      setError('请输入Cookie信息')
      return
    }

    if (!validateCookieFormat(cookieValue)) {
      setError('Cookie格式不正确')
      return
    }

    setIsValidating(true)
    setError('')

    try {
      const result = await validateCookie(platform, cookieValue)
      setValidationResult(result)
      
      if (!result.valid) {
        if (result.missingFields && result.missingFields.length > 0) {
          setError(`Cookie权限不足，缺少必要字段: ${result.missingFields.join(', ')}`)
        } else {
          setError(result.error || 'Cookie验证失败')
        }
      }
    } catch (err: any) {
      if (err.message.includes('网络')) {
        setError('网络连接异常，请检查网络后重试')
      } else {
        setError('验证失败，请重试')
      }
    } finally {
      setIsValidating(false)
    }
  }

  // 连接账号
  const handleConnect = async () => {
    if (!validationResult?.valid || !validationResult.accountInfo) {
      return
    }

    setIsConnecting(true)
    setError('')

    try {
      const result = await connectChannel({
        platform,
        cookie: cookieValue,
        accountInfo: validationResult.accountInfo
      })

      if (result.success) {
        onSuccess(result.channel)
        handleClose()
      }
    } catch (err: any) {
      if (err.code === 'DUPLICATE_CHANNEL') {
        setError('该账号已存在')
      } else {
        setError('连接失败，请重试')
        onError(err)
      }
    } finally {
      setIsConnecting(false)
    }
  }

  // 键盘事件处理
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      handleClose()
    }
  }

  if (!isOpen) return null

  return (
    <Modal 
      isOpen={isOpen} 
      onClose={handleClose}
      size="2xl"
      onKeyDown={handleKeyDown}
    >
      <ModalContent>
        <ModalHeader>
          连接{platformMap[platform as keyof typeof platformMap] || platform}账号
        </ModalHeader>
        
        <ModalBody className="space-y-4">
          <div>
            <p className="text-gray-600 mb-4">
              请输入您的{platformMap[platform as keyof typeof platformMap]}Cookie信息
            </p>
            
            {/* Cookie输入区域 */}
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <label className="text-sm font-medium">Cookie信息</label>
                <div className="flex gap-2">
                  <Link href="#" size="sm">如何获取Cookie？</Link>
                  <Button
                    size="sm"
                    variant="light"
                    onClick={clearCookie}
                    data-testid="clear-cookie-button"
                  >
                    清空
                  </Button>
                </div>
              </div>
              
              <Textarea
                data-testid="cookie-input"
                placeholder="请粘贴完整的Cookie字符串"
                value={showMasked && cookieValue ? maskCookie(cookieValue) : cookieValue}
                onChange={(e) => handleCookieChange(e.target.value)}
                onBlur={handleCookieBlur}
                onFocus={handleCookieFocus}
                minRows={3}
                maxRows={6}
                isInvalid={!!error}
                errorMessage={error}
              />
            </div>

            {/* 验证按钮 */}
            <div className="mt-4">
              <Button
                color="primary"
                onClick={handleValidateCookie}
                isLoading={isValidating}
                isDisabled={!cookieValue.trim() || isValidating}
                className="w-full"
              >
                {isValidating ? (
                  <>
                    <Spinner size="sm" data-testid="validation-spinner" />
                    验证中...
                  </>
                ) : (
                  '验证Cookie'
                )}
              </Button>
            </div>
          </div>

          {/* 验证结果 */}
          {validationResult?.valid && validationResult.accountInfo && (
            <Card>
              <CardBody>
                <div className="flex items-center gap-4">
                  <Avatar
                    src={validationResult.accountInfo.avatar}
                    name={validationResult.accountInfo.name}
                    size="md"
                  />
                  <div>
                    <div className="font-medium">{validationResult.accountInfo.name}</div>
                    <div className="text-sm text-gray-500">
                      {validationResult.accountInfo.id}
                    </div>
                  </div>
                  <Chip color="success" variant="flat" size="sm">
                    Cookie验证成功
                  </Chip>
                </div>
              </CardBody>
            </Card>
          )}

          {/* 错误信息 */}
          {error && (
            <Card>
              <CardBody>
                <div className="text-red-600">
                  {error}
                  {error.includes('权限不足') && (
                    <div className="mt-2">
                      <Link href="#" size="sm">查看Cookie获取指南</Link>
                    </div>
                  )}
                  {error.includes('已存在') && (
                    <div className="mt-2">
                      <Button size="sm" variant="light">
                        查看现有账号
                      </Button>
                    </div>
                  )}
                  {error.includes('网络') && (
                    <div className="mt-2">
                      <Button size="sm" onClick={handleValidateCookie}>
                        重试
                      </Button>
                    </div>
                  )}
                </div>
              </CardBody>
            </Card>
          )}
        </ModalBody>

        <ModalFooter>
          <Button variant="light" onClick={handleClose}>
            取消
          </Button>
          
          {validationResult?.valid ? (
            <Button
              color="primary"
              onClick={handleConnect}
              isLoading={isConnecting}
              isDisabled={isConnecting}
            >
              {isConnecting ? (
                <>
                  <Spinner size="sm" data-testid="connection-progress" />
                  连接中...
                </>
              ) : (
                '连接账号'
              )}
            </Button>
          ) : (
            <Button
              color="primary"
              onClick={handleValidateCookie}
              isLoading={isValidating}
              isDisabled={!cookieValue.trim() || isValidating}
            >
              {error && !error.includes('格式') ? '重新验证' : '验证Cookie'}
            </Button>
          )}
        </ModalFooter>
      </ModalContent>
    </Modal>
  )
}
