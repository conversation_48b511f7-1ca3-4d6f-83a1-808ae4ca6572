/**
 * ChannelList组件
 * 
 * 渠道列表组件，支持：
 * - 渠道列表展示
 * - 排序和筛选
 * - 状态显示
 * - 操作按钮
 */

import React, { useState } from 'react'
import { 
  Card, 
  CardBody, 
  Button, 
  Chip, 
  Avatar, 
  Dropdown, 
  DropdownTrigger, 
  DropdownMenu, 
  DropdownItem,
  Checkbox,
  CheckboxGroup,
  Spinner
} from '@heroui/react'

// 类型定义
export interface Channel {
  id: string
  platform: string
  platformAccountId: string
  displayName: string
  avatarUrl?: string
  status: 'connected' | 'disconnected' | 'error' | 'paused'
  isDeleted: boolean
  lastConnectedAt: string
  lastActiveAt: string
  createdAt: string
  updatedAt: string
}

export interface ChannelListProps {
  channels: Channel[]
  loading?: boolean
  onEdit: (channel: Channel) => void
  onDelete: (channel: Channel) => void
  onConnect: (channel: Channel) => void
  onSort: (field: string, direction: 'asc' | 'desc') => void
  onFilter: (filters: Record<string, string[]>) => void
}

// 状态映射
const statusMap = {
  connected: { label: '已连接', color: 'success' as const },
  disconnected: { label: '已断开', color: 'warning' as const },
  error: { label: '连接异常', color: 'danger' as const },
  paused: { label: '已暂停', color: 'default' as const }
}

// 平台映射
const platformMap = {
  xianyu: '闲鱼',
  douyin: '抖音',
  xiaohongshu: '小红书'
}

export const ChannelList: React.FC<ChannelListProps> = ({
  channels,
  loading = false,
  onEdit,
  onDelete,
  onConnect,
  onSort,
  onFilter
}) => {
  const [sortField, setSortField] = useState<string>('')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc')
  const [filters, setFilters] = useState<Record<string, string[]>>({})

  // 过滤掉已删除的渠道
  const activeChannels = channels.filter(channel => !channel.isDeleted)

  // 处理排序
  const handleSort = (field: string) => {
    const direction = sortField === field && sortDirection === 'desc' ? 'asc' : 'desc'
    setSortField(field)
    setSortDirection(direction)
    onSort(field, direction)
  }

  // 处理筛选
  const handleFilter = (newFilters: Record<string, string[]>) => {
    setFilters(newFilters)
    onFilter(newFilters)
  }

  // 清除筛选
  const clearFilters = () => {
    setFilters({})
    onFilter({})
  }

  // 加载状态
  if (loading) {
    return (
      <div className="flex justify-center items-center h-64" data-testid="channel-list-loading">
        <Spinner size="lg" />
      </div>
    )
  }

  // 空状态
  if (activeChannels.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-500 text-lg mb-2">暂无渠道</div>
        <div className="text-gray-400 text-sm">点击"添加渠道"开始连接您的第一个平台账号</div>
      </div>
    )
  }

  return (
    <div 
      className={`space-y-4 ${window.innerWidth <= 768 ? 'mobile-layout' : ''}`}
      data-testid="channel-list-container"
    >
      {/* 工具栏 */}
      <div className="flex justify-between items-center">
        <div className="flex gap-2">
          {/* 排序按钮 */}
          <Dropdown>
            <DropdownTrigger>
              <Button variant="bordered" data-testid="sort-button">
                排序
              </Button>
            </DropdownTrigger>
            <DropdownMenu>
              <DropdownItem key="createdAt" onClick={() => handleSort('createdAt')}>
                创建时间
              </DropdownItem>
              <DropdownItem key="lastActiveAt" onClick={() => handleSort('lastActiveAt')}>
                最后活跃
              </DropdownItem>
              <DropdownItem key="status" onClick={() => handleSort('status')}>
                连接状态
              </DropdownItem>
            </DropdownMenu>
          </Dropdown>

          {/* 筛选按钮 */}
          <Dropdown>
            <DropdownTrigger>
              <Button variant="bordered" data-testid="filter-button">
                筛选
              </Button>
            </DropdownTrigger>
            <DropdownMenu>
              <DropdownItem key="platform">
                <div className="p-2">
                  <div className="font-medium mb-2">平台</div>
                  <CheckboxGroup
                    value={filters.platform || []}
                    onValueChange={(value) => handleFilter({ ...filters, platform: value })}
                  >
                    <Checkbox value="xianyu">闲鱼</Checkbox>
                    <Checkbox value="douyin">抖音</Checkbox>
                    <Checkbox value="xiaohongshu">小红书</Checkbox>
                  </CheckboxGroup>
                </div>
              </DropdownItem>
              <DropdownItem key="status">
                <div className="p-2">
                  <div className="font-medium mb-2">状态</div>
                  <CheckboxGroup
                    value={filters.status || []}
                    onValueChange={(value) => handleFilter({ ...filters, status: value })}
                  >
                    <Checkbox value="connected">已连接</Checkbox>
                    <Checkbox value="disconnected">已断开</Checkbox>
                    <Checkbox value="error">连接异常</Checkbox>
                    <Checkbox value="paused">已暂停</Checkbox>
                  </CheckboxGroup>
                </div>
              </DropdownItem>
              <DropdownItem key="clear">
                <Button size="sm" variant="light" onClick={clearFilters}>
                  清除筛选
                </Button>
              </DropdownItem>
            </DropdownMenu>
          </Dropdown>
        </div>
      </div>

      {/* 渠道列表 */}
      <div className="grid gap-4">
        {activeChannels.map((channel) => (
          <Card key={channel.id} className="w-full">
            <CardBody className="flex flex-row items-center justify-between p-4">
              <div className="flex items-center gap-4">
                <Avatar
                  src={channel.avatarUrl}
                  name={channel.displayName}
                  size="md"
                />
                <div>
                  <div className="font-medium">{channel.displayName}</div>
                  <div className="text-sm text-gray-500">
                    {platformMap[channel.platform as keyof typeof platformMap] || channel.platform}
                  </div>
                  <div className="text-xs text-gray-400">
                    {channel.platformAccountId}
                  </div>
                </div>
              </div>

              <div className="flex items-center gap-4">
                <Chip
                  color={statusMap[channel.status]?.color || 'default'}
                  variant="flat"
                  size="sm"
                  data-testid="status-chip"
                >
                  {statusMap[channel.status]?.label || channel.status}
                </Chip>

                <div className="flex gap-2">
                  <Button
                    size="sm"
                    variant="light"
                    onClick={() => onEdit(channel)}
                    data-testid="edit-channel-button"
                  >
                    编辑
                  </Button>
                  
                  {channel.status !== 'connected' && (
                    <Button
                      size="sm"
                      color="primary"
                      variant="light"
                      onClick={() => onConnect(channel)}
                      data-testid="reconnect-channel-button"
                    >
                      重连
                    </Button>
                  )}
                  
                  <Button
                    size="sm"
                    color="danger"
                    variant="light"
                    onClick={() => onDelete(channel)}
                    data-testid="delete-channel-button"
                  >
                    删除
                  </Button>
                </div>
              </div>
            </CardBody>
          </Card>
        ))}
      </div>
    </div>
  )
}
