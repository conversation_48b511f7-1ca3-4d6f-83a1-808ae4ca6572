/**
 * ChannelMonitor组件
 * 
 * 渠道状态监控组件，支持：
 * - 实时状态显示
 * - 重连机制
 * - Cookie失效检测
 * - 监控数据统计
 */

import React, { useState, useEffect, useRef, useCallback } from 'react'
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Chip,
  Avatar,
  Switch,
  Spinner,
  Progress,
  Divider
} from '@heroui/react'
import { getChannelStatus, reconnectChannel, updateCookie } from '../services/channelApi'

// 类型定义
export interface ChannelMonitorProps {
  channel: {
    id: string
    platform: string
    platformAccountId: string
    displayName: string
    status: 'connected' | 'disconnected' | 'error' | 'cookie_expired' | 'paused'
    lastConnectedAt: string
    lastActiveAt: string
    connectionLogs?: Array<{
      id: string
      status: string
      timestamp: string
      message: string
    }>
  }
  onReconnect: (channelId: string) => void
  onUpdateCookie: (channelId: string) => void
  onStatusChange: (statusData: any) => void
}

interface MonitoringStats {
  todayConnections: number
  averageResponseTime: number
  todayErrors: number
  connectionQuality: number
}

// 状态映射
const statusMap = {
  connected: { label: '已连接', color: 'success' as const, icon: '🟢' },
  disconnected: { label: '已断开', color: 'warning' as const, icon: '🟡' },
  error: { label: '连接异常', color: 'danger' as const, icon: '🔴' },
  cookie_expired: { label: 'Cookie已过期', color: 'danger' as const, icon: '🔴' },
  paused: { label: '已暂停', color: 'default' as const, icon: '⏸️' }
}

// 平台映射
const platformMap = {
  xianyu: '闲鱼',
  douyin: '抖音',
  xiaohongshu: '小红书'
}

export const ChannelMonitor: React.FC<ChannelMonitorProps> = ({
  channel,
  onReconnect,
  onUpdateCookie,
  onStatusChange
}) => {
  const [isReconnecting, setIsReconnecting] = useState(false)
  const [autoReconnect, setAutoReconnect] = useState(false)
  const [monitoringStats, setMonitoringStats] = useState<MonitoringStats>({
    todayConnections: 0,
    averageResponseTime: 0,
    todayErrors: 0,
    connectionQuality: 85
  })
  const [wsConnected, setWsConnected] = useState(false)
  const [error, setError] = useState<string>('')
  const [isExporting, setIsExporting] = useState(false)
  
  const wsRef = useRef<WebSocket | null>(null)
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const statusCheckIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const lastStatusUpdateRef = useRef<number>(0)

  // 防抖处理状态更新
  const debouncedStatusChange = useCallback((statusData: any) => {
    const now = Date.now()
    if (now - lastStatusUpdateRef.current > 1000) { // 1秒防抖
      lastStatusUpdateRef.current = now
      onStatusChange(statusData)
    }
  }, [onStatusChange])

  // 建立WebSocket连接
  const connectWebSocket = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return
    }

    try {
      const wsUrl = `ws://localhost:8000/ws/channel/${channel.id}/monitor`
      wsRef.current = new WebSocket(wsUrl)

      wsRef.current.addEventListener('open', () => {
        setWsConnected(true)
        setError('')
      })

      wsRef.current.addEventListener('message', (event) => {
        try {
          const data = JSON.parse(event.data)
          
          switch (data.type) {
            case 'status_change':
              debouncedStatusChange(data)
              break
            case 'cookie_expired':
              debouncedStatusChange(data)
              break
            case 'stats_update':
              setMonitoringStats(data.stats)
              break
          }
        } catch (err) {
          console.error('Failed to parse WebSocket message:', err)
        }
      })

      wsRef.current.addEventListener('error', () => {
        setWsConnected(false)
        setError('监控连接异常')
      })

      wsRef.current.addEventListener('close', () => {
        setWsConnected(false)
        // 自动重连
        setTimeout(connectWebSocket, 5000)
      })
    } catch (err) {
      setError('无法建立监控连接')
    }
  }, [channel.id, debouncedStatusChange])

  // 定期检查连接状态
  const checkChannelStatus = useCallback(async () => {
    try {
      const status = await getChannelStatus(channel.id)
      if (status.status !== channel.status) {
        debouncedStatusChange({
          type: 'status_change',
          channelId: channel.id,
          status: status.status,
          timestamp: new Date().toISOString()
        })
      }
    } catch (err) {
      console.error('Failed to check channel status:', err)
    }
  }, [channel.id, channel.status, debouncedStatusChange])

  // 手动重连
  const handleReconnect = async () => {
    setIsReconnecting(true)
    setError('')

    try {
      await reconnectChannel(channel.id)
      onReconnect(channel.id)
    } catch (err: any) {
      setError('重连失败，请检查网络连接')
    } finally {
      setIsReconnecting(false)
    }
  }

  // 自动重连逻辑
  useEffect(() => {
    if (autoReconnect && channel.status === 'disconnected') {
      reconnectTimeoutRef.current = setTimeout(() => {
        handleReconnect()
      }, 60000) // 1分钟后自动重连
    }

    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current)
      }
    }
  }, [autoReconnect, channel.status])

  // 组件初始化
  useEffect(() => {
    connectWebSocket()
    
    // 定期检查状态
    statusCheckIntervalRef.current = setInterval(checkChannelStatus, 30000)

    return () => {
      if (wsRef.current) {
        wsRef.current.close()
      }
      if (statusCheckIntervalRef.current) {
        clearInterval(statusCheckIntervalRef.current)
      }
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current)
      }
    }
  }, [connectWebSocket, checkChannelStatus])

  // 格式化时间
  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // 导出监控数据
  const handleExportData = async () => {
    setIsExporting(true)
    try {
      // 模拟导出过程
      await new Promise(resolve => setTimeout(resolve, 2000))
      // 实际实现中会调用API导出数据
    } finally {
      setIsExporting(false)
    }
  }

  return (
    <div className="space-y-4" data-testid="channel-monitor-container">
      {/* 标题栏 */}
      <Card>
        <CardHeader className="flex justify-between items-center">
          <div className="flex items-center gap-4">
            <h3 className="text-lg font-semibold">渠道监控</h3>
            <Chip
              color={wsConnected ? 'success' : 'danger'}
              variant="flat"
              size="sm"
            >
              {wsConnected ? '监控中' : '连接断开'}
            </Chip>
          </div>
          <Button
            size="sm"
            variant="light"
            onClick={handleExportData}
            isLoading={isExporting}
          >
            {isExporting ? '数据导出中...' : '导出数据'}
          </Button>
        </CardHeader>
      </Card>

      {/* 渠道基本信息 */}
      <Card>
        <CardBody>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Avatar name={channel.displayName} size="md" />
              <div>
                <div className="font-medium">{channel.displayName}</div>
                <div className="text-sm text-gray-500">
                  {platformMap[channel.platform as keyof typeof platformMap]} • {channel.platformAccountId}
                </div>
              </div>
            </div>
            
            <div className="flex items-center gap-4">
              <Chip
                color={statusMap[channel.status].color}
                variant="flat"
                data-testid={`status-${channel.status}`}
              >
                <span data-testid="channel-status-indicator">
                  {statusMap[channel.status].icon} {statusMap[channel.status].label}
                </span>
              </Chip>
              
              {channel.status === 'cookie_expired' && (
                <Chip color="danger" variant="flat" data-testid="cookie-status-expired">
                  Cookie已过期
                </Chip>
              )}
            </div>
          </div>
          
          <Divider className="my-4" />
          
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-500">最后活跃:</span>
              <span className="ml-2">{formatTime(channel.lastActiveAt)}</span>
            </div>
            <div>
              <span className="text-gray-500">连接时间:</span>
              <span className="ml-2">{formatTime(channel.lastConnectedAt)}</span>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* 连接控制 */}
      <Card>
        <CardBody>
          <div className="flex justify-between items-center mb-4">
            <h4 className="font-medium">连接控制</h4>
            <Switch
              size="sm"
              isSelected={autoReconnect}
              onValueChange={setAutoReconnect}
              data-testid="auto-reconnect-switch"
            >
              自动重连
            </Switch>
          </div>
          
          <div className="flex gap-2">
            <Button
              color="primary"
              variant="light"
              onClick={handleReconnect}
              isLoading={isReconnecting}
              isDisabled={channel.status === 'connected'}
            >
              {isReconnecting ? (
                <>
                  <Spinner size="sm" data-testid="reconnect-spinner" />
                  重连中...
                </>
              ) : (
                '重新连接'
              )}
            </Button>
            
            {channel.status === 'cookie_expired' && (
              <Button
                color="warning"
                variant="light"
                onClick={() => onUpdateCookie(channel.id)}
              >
                更新Cookie
              </Button>
            )}
            
            {channel.status === 'error' && (
              <Button size="sm" variant="light">
                检查网络连接
              </Button>
            )}
          </div>
          
          {error && (
            <div className="mt-2 text-red-600 text-sm">{error}</div>
          )}
        </CardBody>
      </Card>

      {/* 连接质量 */}
      <Card>
        <CardBody>
          <h4 className="font-medium mb-4">连接质量</h4>
          <div className="space-y-3">
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>连接稳定性</span>
                <span>{monitoringStats.connectionQuality}%</span>
              </div>
              <Progress
                value={monitoringStats.connectionQuality}
                color={monitoringStats.connectionQuality > 80 ? 'success' : 'warning'}
                data-testid="connection-quality"
              />
            </div>
          </div>
        </CardBody>
      </Card>

      {/* 统计信息 */}
      <div className="grid grid-cols-2 gap-4">
        <Card>
          <CardBody>
            <h4 className="font-medium mb-4">连接统计</h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>今日连接次数:</span>
                <span>{monitoringStats.todayConnections}</span>
              </div>
              <div className="flex justify-between">
                <span>平均响应时间:</span>
                <span>{monitoringStats.averageResponseTime}ms</span>
              </div>
            </div>
          </CardBody>
        </Card>
        
        <Card>
          <CardBody>
            <h4 className="font-medium mb-4">错误统计</h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>今日错误次数:</span>
                <span>{monitoringStats.todayErrors}</span>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>

      {/* 连接历史 */}
      <Card>
        <CardBody>
          <h4 className="font-medium mb-4">连接历史</h4>
          <div className="space-y-2">
            {channel.connectionLogs?.slice(0, 5).map((log) => (
              <div key={log.id} className="flex justify-between items-center text-sm">
                <span>{log.message}</span>
                <span className="text-gray-500">{formatTime(log.timestamp)}</span>
              </div>
            )) || (
              <div className="text-gray-500 text-sm">暂无连接历史</div>
            )}
          </div>
        </CardBody>
      </Card>
    </div>
  )
}
