/**
 * ChannelManagement页面
 * 
 * 渠道管理主页面，集成：
 * - 渠道列表展示
 * - 添加和编辑渠道
 * - 删除和恢复操作
 * - 状态监控
 * - 搜索和筛选
 */

import React, { useState, useEffect } from 'react'
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Input,
  Select,
  SelectItem,
  Tabs,
  Tab,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  Spinner,
  Chip,
  Checkbox,
  useDisclosure
} from '@heroui/react'
import { ChannelList } from '../components/ChannelList'
import { ChannelConnection } from '../components/ChannelConnection'
import { ChannelMonitor } from '../components/ChannelMonitor'
import { channelApi, connectChannel, reconnectChannel } from '../services/channelApi'

// 类型定义
interface Channel {
  id: string
  platform: string
  platformAccountId: string
  displayName: string
  avatarUrl?: string
  status: 'connected' | 'disconnected' | 'error' | 'paused'
  isDeleted: boolean
  lastConnectedAt: string
  lastActiveAt: string
  createdAt: string
  updatedAt: string
}

interface Filters {
  search: string
  platform: string
  status: string
  deleted: boolean
}

// 平台选项
const platformOptions = [
  { key: 'all', label: '全部平台' },
  { key: 'xianyu', label: '闲鱼' },
  { key: 'douyin', label: '抖音' },
  { key: 'xiaohongshu', label: '小红书' }
]

// 状态选项
const statusOptions = [
  { key: 'all', label: '全部状态' },
  { key: 'connected', label: '已连接' },
  { key: 'disconnected', label: '已断开' },
  { key: 'error', label: '连接异常' },
  { key: 'paused', label: '已暂停' }
]

export const ChannelManagement: React.FC = () => {
  // 状态管理
  const [channels, setChannels] = useState<Channel[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string>('')
  const [filters, setFilters] = useState<Filters>({
    search: '',
    platform: 'all',
    status: 'all',
    deleted: false
  })
  const [selectedChannels, setSelectedChannels] = useState<string[]>([])
  const [currentTab, setCurrentTab] = useState('active')
  const [monitoringChannel, setMonitoringChannel] = useState<Channel | null>(null)
  const [editingChannel, setEditingChannel] = useState<Channel | null>(null)
  
  // 对话框状态
  const { isOpen: isPlatformModalOpen, onOpen: onPlatformModalOpen, onClose: onPlatformModalClose } = useDisclosure()
  const { isOpen: isConnectionModalOpen, onOpen: onConnectionModalOpen, onClose: onConnectionModalClose } = useDisclosure()
  const { isOpen: isEditModalOpen, onOpen: onEditModalOpen, onClose: onEditModalClose } = useDisclosure()
  const { isOpen: isDeleteModalOpen, onOpen: onDeleteModalOpen, onClose: onDeleteModalClose } = useDisclosure()
  const { isOpen: isMonitorModalOpen, onOpen: onMonitorModalOpen, onClose: onMonitorModalClose } = useDisclosure()
  
  const [selectedPlatform, setSelectedPlatform] = useState<string>('')
  const [channelToDelete, setChannelToDelete] = useState<Channel | null>(null)

  // 加载渠道列表
  const loadChannels = async () => {
    try {
      setLoading(true)
      setError('')
      
      const params: any = {}
      if (filters.search) params.search = filters.search
      if (filters.platform !== 'all') params.platform = filters.platform
      if (filters.status !== 'all') params.status = filters.status
      if (filters.deleted) params.deleted = true
      
      const response = await channelApi.getChannels(params)
      setChannels(response.channels)
    } catch (err: any) {
      setError(err.message.includes('网络') ? '网络连接异常' : '加载渠道列表失败')
    } finally {
      setLoading(false)
    }
  }

  // 初始加载
  useEffect(() => {
    loadChannels()
  }, [filters])

  // 处理搜索
  const handleSearch = (value: string) => {
    setFilters(prev => ({ ...prev, search: value }))
  }

  // 处理筛选
  const handleFilter = (key: keyof Filters, value: string | boolean) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  // 处理标签切换
  const handleTabChange = (key: string) => {
    setCurrentTab(key)
    setFilters(prev => ({ ...prev, deleted: key === 'history' }))
  }

  // 处理添加渠道
  const handleAddChannel = () => {
    onPlatformModalOpen()
  }

  // 处理平台选择
  const handlePlatformSelect = (platform: string) => {
    setSelectedPlatform(platform)
    onPlatformModalClose()
    onConnectionModalOpen()
  }

  // 处理连接成功
  const handleConnectionSuccess = async (channel: Channel) => {
    onConnectionModalClose()
    await loadChannels()
    setError('')
  }

  // 处理连接错误
  const handleConnectionError = (error: Error) => {
    setError(error.message)
  }

  // 处理编辑渠道
  const handleEditChannel = (channel: Channel) => {
    setEditingChannel(channel)
    onEditModalOpen()
  }

  // 处理删除渠道
  const handleDeleteChannel = (channel: Channel) => {
    setChannelToDelete(channel)
    onDeleteModalOpen()
  }

  // 确认删除
  const confirmDelete = async () => {
    if (!channelToDelete) return
    
    try {
      await channelApi.deleteChannel(channelToDelete.id)
      onDeleteModalClose()
      await loadChannels()
      setError('')
    } catch (err: any) {
      setError('删除失败，请重试')
    }
  }

  // 处理恢复渠道
  const handleRestoreChannel = async (channel: Channel) => {
    try {
      await channelApi.restoreChannel(channel.id)
      await loadChannels()
      setError('')
    } catch (err: any) {
      setError('恢复失败，请重试')
    }
  }

  // 处理重连
  const handleReconnect = async (channelId: string) => {
    try {
      await reconnectChannel(channelId)
      await loadChannels()
    } catch (err: any) {
      setError('重连失败，请重试')
    }
  }

  // 处理监控
  const handleMonitor = (channel: Channel) => {
    setMonitoringChannel(channel)
    onMonitorModalOpen()
  }

  // 处理排序
  const handleSort = (field: string, direction: 'asc' | 'desc') => {
    const sortedChannels = [...channels].sort((a, b) => {
      const aValue = a[field as keyof Channel]
      const bValue = b[field as keyof Channel]
      
      if (direction === 'asc') {
        return aValue > bValue ? 1 : -1
      } else {
        return aValue < bValue ? 1 : -1
      }
    })
    
    setChannels(sortedChannels)
  }

  // 处理筛选（来自ChannelList组件）
  const handleChannelFilter = (filterData: Record<string, string[]>) => {
    console.log('Channel filter:', filterData)
  }

  return (
    <div className={`container mx-auto p-6 space-y-6 ${window.innerWidth <= 768 ? 'mobile-layout' : ''}`} data-testid="mobile-layout">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">渠道管理</h1>
        <div className="flex gap-2">
          <Button color="primary" onClick={handleAddChannel}>
            添加渠道
          </Button>
          <Button variant="light" onClick={() => handleMonitor(channels[0])}>
            监控面板
          </Button>
        </div>
      </div>

      {/* 搜索和筛选 */}
      <Card>
        <CardBody>
          <div className="flex flex-wrap gap-4">
            <Input
              placeholder="搜索渠道名称或账号"
              value={filters.search}
              onChange={(e) => handleSearch(e.target.value)}
              className="flex-1 min-w-64"
            />
            <Select
              placeholder="选择平台"
              selectedKeys={[filters.platform]}
              onSelectionChange={(keys) => handleFilter('platform', Array.from(keys)[0] as string)}
              className="w-40"
              data-testid="platform-filter"
            >
              {platformOptions.map((option) => (
                <SelectItem key={option.key} value={option.key}>
                  {option.label}
                </SelectItem>
              ))}
            </Select>
            <Select
              placeholder="选择状态"
              selectedKeys={[filters.status]}
              onSelectionChange={(keys) => handleFilter('status', Array.from(keys)[0] as string)}
              className="w-40"
              data-testid="status-filter"
            >
              {statusOptions.map((option) => (
                <SelectItem key={option.key} value={option.key}>
                  {option.label}
                </SelectItem>
              ))}
            </Select>
          </div>
        </CardBody>
      </Card>

      {/* 标签页 */}
      <Tabs selectedKey={currentTab} onSelectionChange={(key) => handleTabChange(key as string)}>
        <Tab key="active" title="活跃渠道">
          {/* 批量操作 */}
          {selectedChannels.length > 0 && (
            <Card className="mb-4">
              <CardBody>
                <div className="flex justify-between items-center">
                  <span>已选择 {selectedChannels.length} 个渠道</span>
                  <div className="flex gap-2">
                    <Button size="sm" color="danger" variant="light">
                      批量删除
                    </Button>
                    <Button size="sm" variant="light">
                      批量暂停
                    </Button>
                  </div>
                </div>
              </CardBody>
            </Card>
          )}

          {/* 渠道列表 */}
          {loading ? (
            <div className="flex justify-center py-8" data-testid="loading-spinner">
              <Spinner size="lg" />
            </div>
          ) : error ? (
            <Card>
              <CardBody className="text-center py-8">
                <div className="text-red-600 mb-4">{error}</div>
                <Button onClick={loadChannels}>重试</Button>
              </CardBody>
            </Card>
          ) : (
            <ChannelList
              channels={channels}
              onEdit={handleEditChannel}
              onDelete={handleDeleteChannel}
              onConnect={handleReconnect}
              onSort={handleSort}
              onFilter={handleChannelFilter}
              selectedChannels={selectedChannels}
              onSelectionChange={setSelectedChannels}
            />
          )}
        </Tab>
        
        <Tab key="history" title="历史渠道">
          <div className="text-center py-4">
            <h3 className="text-lg font-medium mb-4">已删除的渠道</h3>
            {loading ? (
              <Spinner size="lg" />
            ) : channels.length === 0 ? (
              <div className="text-gray-500">暂无已删除的渠道</div>
            ) : (
              <div className="space-y-4">
                {channels.map((channel) => (
                  <Card key={channel.id}>
                    <CardBody>
                      <div className="flex justify-between items-center">
                        <div>
                          <div className="font-medium">{channel.displayName}</div>
                          <div className="text-sm text-gray-500">{channel.platform}</div>
                        </div>
                        <Button
                          size="sm"
                          color="primary"
                          variant="light"
                          onClick={() => handleRestoreChannel(channel)}
                        >
                          恢复
                        </Button>
                      </div>
                    </CardBody>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </Tab>
      </Tabs>

      {/* 平台选择对话框 */}
      <Modal isOpen={isPlatformModalOpen} onClose={onPlatformModalClose} data-testid="platform-selection-modal">
        <ModalContent>
          <ModalHeader>选择平台</ModalHeader>
          <ModalBody>
            <div className="grid grid-cols-2 gap-4">
              <Button
                variant="bordered"
                className="h-20"
                onClick={() => handlePlatformSelect('xianyu')}
                data-testid="platform-xianyu"
              >
                <div className="text-center">
                  <div className="text-2xl mb-2">🛒</div>
                  <div>闲鱼</div>
                </div>
              </Button>
              <Button
                variant="bordered"
                className="h-20"
                onClick={() => handlePlatformSelect('douyin')}
                data-testid="platform-douyin"
              >
                <div className="text-center">
                  <div className="text-2xl mb-2">🎵</div>
                  <div>抖音</div>
                </div>
              </Button>
            </div>
          </ModalBody>
        </ModalContent>
      </Modal>

      {/* 连接渠道对话框 */}
      <ChannelConnection
        isOpen={isConnectionModalOpen}
        platform={selectedPlatform}
        onClose={onConnectionModalClose}
        onSuccess={handleConnectionSuccess}
        onError={handleConnectionError}
      />

      {/* 编辑渠道对话框 */}
      <Modal isOpen={isEditModalOpen} onClose={onEditModalClose} data-testid="channel-edit-modal">
        <ModalContent>
          <ModalHeader>编辑渠道</ModalHeader>
          <ModalBody>
            {editingChannel && (
              <div className="space-y-4">
                <Input
                  label="显示名称"
                  value={editingChannel.displayName}
                  onChange={(e) => setEditingChannel({
                    ...editingChannel,
                    displayName: e.target.value
                  })}
                />
              </div>
            )}
          </ModalBody>
          <ModalFooter>
            <Button variant="light" onClick={onEditModalClose}>
              取消
            </Button>
            <Button color="primary">
              保存
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* 删除确认对话框 */}
      <Modal isOpen={isDeleteModalOpen} onClose={onDeleteModalClose}>
        <ModalContent>
          <ModalHeader>确认删除</ModalHeader>
          <ModalBody>
            <div>确定要删除渠道 "{channelToDelete?.displayName}" 吗？</div>
            <div className="text-sm text-gray-500 mt-2">
              删除后可以在历史渠道中恢复
            </div>
          </ModalBody>
          <ModalFooter>
            <Button variant="light" onClick={onDeleteModalClose}>
              取消
            </Button>
            <Button color="danger" onClick={confirmDelete}>
              确定删除
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* 监控对话框 */}
      <Modal 
        isOpen={isMonitorModalOpen} 
        onClose={onMonitorModalClose}
        size="5xl"
        scrollBehavior="inside"
      >
        <ModalContent>
          <ModalHeader>渠道监控</ModalHeader>
          <ModalBody>
            {monitoringChannel && (
              <ChannelMonitor
                channel={monitoringChannel}
                onReconnect={handleReconnect}
                onUpdateCookie={(channelId) => console.log('Update cookie:', channelId)}
                onStatusChange={(statusData) => console.log('Status change:', statusData)}
              />
            )}
          </ModalBody>
        </ModalContent>
      </Modal>

      {/* 成功消息 */}
      {error === '' && channels.length > 0 && (
        <div className="fixed bottom-4 right-4 z-50">
          <Chip color="success" variant="flat">
            渠道连接成功
          </Chip>
        </div>
      )}
    </div>
  )
}
