/**
 * ChannelManagement页面测试
 * 
 * 基于BDD剧本测试渠道管理页面的完整用户交互流程：
 * - 页面渲染和导航
 * - 渠道列表管理
 * - 添加和编辑渠道
 * - 删除和恢复操作
 * - 状态监控集成
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { BrowserRouter } from 'react-router-dom'
import { ChannelManagement } from '../ChannelManagement'

// Mock API调用
const mockGetChannels = vi.fn()
const mockDeleteChannel = vi.fn()
const mockRestoreChannel = vi.fn()
const mockConnectChannel = vi.fn()

vi.mock('../../services/channelApi', () => ({
  channelApi: {
    getChannels: mockGetChannels,
    deleteChannel: mockDeleteChannel,
    restoreChannel: mockRestoreChannel
  },
  connectChannel: mockConnectChannel
}))

// Mock子组件
vi.mock('../../components/ChannelList', () => ({
  ChannelList: ({ channels, onEdit, onDelete, onConnect, onSort, onFilter }: any) => (
    <div data-testid="channel-list">
      <div>渠道数量: {channels.length}</div>
      {channels.map((channel: any) => (
        <div key={channel.id} data-testid={`channel-${channel.id}`}>
          <span>{channel.displayName}</span>
          <button onClick={() => onEdit(channel)}>编辑</button>
          <button onClick={() => onDelete(channel)}>删除</button>
          <button onClick={() => onConnect(channel)}>连接</button>
        </div>
      ))}
    </div>
  )
}))

vi.mock('../../components/ChannelConnection', () => ({
  ChannelConnection: ({ isOpen, platform, onClose, onSuccess }: any) => (
    isOpen ? (
      <div data-testid="channel-connection-modal">
        <div>连接{platform}账号</div>
        <button onClick={onClose}>关闭</button>
        <button onClick={() => onSuccess({ id: 'new-channel', platform })}>连接成功</button>
      </div>
    ) : null
  )
}))

vi.mock('../../components/ChannelMonitor', () => ({
  ChannelMonitor: ({ channel, onReconnect, onUpdateCookie }: any) => (
    <div data-testid="channel-monitor">
      <div>监控: {channel.displayName}</div>
      <button onClick={() => onReconnect(channel.id)}>重连</button>
      <button onClick={() => onUpdateCookie(channel.id)}>更新Cookie</button>
    </div>
  )
}))

// Mock数据
const mockChannels = [
  {
    id: '1',
    platform: 'xianyu',
    platformAccountId: 'xianyu_shop_123',
    displayName: '主店铺',
    status: 'connected',
    isDeleted: false,
    lastConnectedAt: '2025-08-01T10:30:00Z',
    lastActiveAt: '2025-08-01T15:45:00Z',
    createdAt: '2025-08-01T10:30:00Z',
    updatedAt: '2025-08-01T15:45:00Z'
  },
  {
    id: '2',
    platform: 'xianyu',
    platformAccountId: 'xianyu_shop_456',
    displayName: '客服店铺',
    status: 'disconnected',
    isDeleted: false,
    lastConnectedAt: '2025-08-02T14:20:00Z',
    lastActiveAt: '2025-08-02T16:30:00Z',
    createdAt: '2025-08-02T14:20:00Z',
    updatedAt: '2025-08-02T16:30:00Z'
  }
]

// 测试包装器
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <BrowserRouter>
    {children}
  </BrowserRouter>
)

describe('ChannelManagement页面', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockGetChannels.mockResolvedValue({
      channels: mockChannels,
      total: mockChannels.length
    })
  })

  describe('页面渲染功能', () => {
    it('应该正确渲染页面标题和导航', async () => {
      render(
        <TestWrapper>
          <ChannelManagement />
        </TestWrapper>
      )

      expect(screen.getByText('渠道管理')).toBeInTheDocument()
      expect(screen.getByText('添加渠道')).toBeInTheDocument()
      expect(screen.getByText('历史渠道')).toBeInTheDocument()
    })

    it('应该加载并显示渠道列表', async () => {
      render(
        <TestWrapper>
          <ChannelManagement />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(mockGetChannels).toHaveBeenCalled()
        expect(screen.getByTestId('channel-list')).toBeInTheDocument()
        expect(screen.getByText('渠道数量: 2')).toBeInTheDocument()
      })
    })

    it('应该显示加载状态', () => {
      mockGetChannels.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 1000)))
      
      render(
        <TestWrapper>
          <ChannelManagement />
        </TestWrapper>
      )

      expect(screen.getByTestId('loading-spinner')).toBeInTheDocument()
    })

    it('应该处理加载错误', async () => {
      mockGetChannels.mockRejectedValue(new Error('加载失败'))
      
      render(
        <TestWrapper>
          <ChannelManagement />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByText('加载渠道列表失败')).toBeInTheDocument()
        expect(screen.getByText('重试')).toBeInTheDocument()
      })
    })
  })

  describe('添加渠道功能', () => {
    it('应该打开添加渠道对话框', async () => {
      const user = userEvent.setup()
      render(
        <TestWrapper>
          <ChannelManagement />
        </TestWrapper>
      )

      const addButton = screen.getByText('添加渠道')
      await user.click(addButton)

      expect(screen.getByTestId('platform-selection-modal')).toBeInTheDocument()
      expect(screen.getByText('选择平台')).toBeInTheDocument()
    })

    it('应该支持选择不同平台', async () => {
      const user = userEvent.setup()
      render(
        <TestWrapper>
          <ChannelManagement />
        </TestWrapper>
      )

      const addButton = screen.getByText('添加渠道')
      await user.click(addButton)

      // 选择闲鱼平台
      const xianyuOption = screen.getByTestId('platform-xianyu')
      await user.click(xianyuOption)

      expect(screen.getByTestId('channel-connection-modal')).toBeInTheDocument()
      expect(screen.getByText('连接xianyu账号')).toBeInTheDocument()
    })

    it('应该处理连接成功', async () => {
      const user = userEvent.setup()
      render(
        <TestWrapper>
          <ChannelManagement />
        </TestWrapper>
      )

      // 打开添加对话框
      const addButton = screen.getByText('添加渠道')
      await user.click(addButton)

      // 选择平台
      const xianyuOption = screen.getByTestId('platform-xianyu')
      await user.click(xianyuOption)

      // 模拟连接成功
      const connectSuccessButton = screen.getByText('连接成功')
      await user.click(connectSuccessButton)

      await waitFor(() => {
        expect(mockGetChannels).toHaveBeenCalledTimes(2) // 初始加载 + 刷新
        expect(screen.getByText('渠道连接成功')).toBeInTheDocument()
      })
    })
  })

  describe('渠道管理功能', () => {
    it('应该支持编辑渠道', async () => {
      const user = userEvent.setup()
      render(
        <TestWrapper>
          <ChannelManagement />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByTestId('channel-list')).toBeInTheDocument()
      })

      // 点击编辑按钮
      const editButton = screen.getAllByText('编辑')[0]
      await user.click(editButton)

      expect(screen.getByTestId('channel-edit-modal')).toBeInTheDocument()
      expect(screen.getByText('编辑渠道')).toBeInTheDocument()
    })

    it('应该支持删除渠道', async () => {
      const user = userEvent.setup()
      mockDeleteChannel.mockResolvedValue({})
      
      render(
        <TestWrapper>
          <ChannelManagement />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByTestId('channel-list')).toBeInTheDocument()
      })

      // 点击删除按钮
      const deleteButton = screen.getAllByText('删除')[0]
      await user.click(deleteButton)

      // 确认删除
      expect(screen.getByText('确认删除')).toBeInTheDocument()
      const confirmButton = screen.getByText('确定删除')
      await user.click(confirmButton)

      await waitFor(() => {
        expect(mockDeleteChannel).toHaveBeenCalledWith('1')
        expect(screen.getByText('渠道删除成功')).toBeInTheDocument()
      })
    })

    it('应该支持批量操作', async () => {
      const user = userEvent.setup()
      render(
        <TestWrapper>
          <ChannelManagement />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByTestId('channel-list')).toBeInTheDocument()
      })

      // 选择多个渠道
      const checkboxes = screen.getAllByRole('checkbox')
      await user.click(checkboxes[0])
      await user.click(checkboxes[1])

      // 批量删除
      const batchDeleteButton = screen.getByText('批量删除')
      expect(batchDeleteButton).toBeEnabled()
      await user.click(batchDeleteButton)

      expect(screen.getByText('确认批量删除')).toBeInTheDocument()
    })
  })

  describe('状态监控功能', () => {
    it('应该显示渠道监控面板', async () => {
      render(
        <TestWrapper>
          <ChannelManagement />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByTestId('channel-list')).toBeInTheDocument()
      })

      // 点击监控按钮
      const monitorButton = screen.getByText('监控面板')
      await userEvent.click(monitorButton)

      expect(screen.getByTestId('channel-monitor')).toBeInTheDocument()
    })

    it('应该支持重连操作', async () => {
      const user = userEvent.setup()
      render(
        <TestWrapper>
          <ChannelManagement />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByTestId('channel-list')).toBeInTheDocument()
      })

      // 点击连接按钮（针对断开连接的渠道）
      const connectButton = screen.getAllByText('连接')[0]
      await user.click(connectButton)

      expect(screen.getByText('重连中...')).toBeInTheDocument()
    })
  })

  describe('搜索和筛选功能', () => {
    it('应该支持搜索渠道', async () => {
      const user = userEvent.setup()
      render(
        <TestWrapper>
          <ChannelManagement />
        </TestWrapper>
      )

      const searchInput = screen.getByPlaceholderText('搜索渠道名称或账号')
      await user.type(searchInput, '主店铺')

      await waitFor(() => {
        expect(mockGetChannels).toHaveBeenCalledWith(
          expect.objectContaining({
            search: '主店铺'
          })
        )
      })
    })

    it('应该支持按状态筛选', async () => {
      const user = userEvent.setup()
      render(
        <TestWrapper>
          <ChannelManagement />
        </TestWrapper>
      )

      const statusFilter = screen.getByTestId('status-filter')
      await user.selectOptions(statusFilter, 'connected')

      await waitFor(() => {
        expect(mockGetChannels).toHaveBeenCalledWith(
          expect.objectContaining({
            status: 'connected'
          })
        )
      })
    })

    it('应该支持按平台筛选', async () => {
      const user = userEvent.setup()
      render(
        <TestWrapper>
          <ChannelManagement />
        </TestWrapper>
      )

      const platformFilter = screen.getByTestId('platform-filter')
      await user.selectOptions(platformFilter, 'xianyu')

      await waitFor(() => {
        expect(mockGetChannels).toHaveBeenCalledWith(
          expect.objectContaining({
            platform: 'xianyu'
          })
        )
      })
    })
  })

  describe('历史渠道功能', () => {
    it('应该切换到历史渠道视图', async () => {
      const user = userEvent.setup()
      render(
        <TestWrapper>
          <ChannelManagement />
        </TestWrapper>
      )

      const historyTab = screen.getByText('历史渠道')
      await user.click(historyTab)

      expect(screen.getByText('已删除的渠道')).toBeInTheDocument()
      expect(mockGetChannels).toHaveBeenCalledWith(
        expect.objectContaining({
          deleted: true
        })
      )
    })

    it('应该支持恢复已删除渠道', async () => {
      const user = userEvent.setup()
      mockRestoreChannel.mockResolvedValue({})
      
      // 模拟已删除渠道数据
      const deletedChannels = [{
        ...mockChannels[0],
        isDeleted: true
      }]
      
      mockGetChannels.mockResolvedValue({
        channels: deletedChannels,
        total: 1
      })

      render(
        <TestWrapper>
          <ChannelManagement />
        </TestWrapper>
      )

      // 切换到历史渠道
      const historyTab = screen.getByText('历史渠道')
      await user.click(historyTab)

      await waitFor(() => {
        const restoreButton = screen.getByText('恢复')
        user.click(restoreButton)
      })

      await waitFor(() => {
        expect(mockRestoreChannel).toHaveBeenCalledWith('1')
        expect(screen.getByText('渠道恢复成功')).toBeInTheDocument()
      })
    })
  })

  describe('响应式设计', () => {
    it('应该在移动端正确显示', () => {
      // 模拟移动端视口
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      })

      render(
        <TestWrapper>
          <ChannelManagement />
        </TestWrapper>
      )

      expect(screen.getByTestId('mobile-layout')).toBeInTheDocument()
    })
  })

  describe('错误处理', () => {
    it('应该处理网络错误', async () => {
      mockGetChannels.mockRejectedValue(new Error('网络错误'))
      
      render(
        <TestWrapper>
          <ChannelManagement />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByText('网络连接异常')).toBeInTheDocument()
        expect(screen.getByText('重试')).toBeInTheDocument()
      })
    })

    it('应该处理删除失败', async () => {
      const user = userEvent.setup()
      mockDeleteChannel.mockRejectedValue(new Error('删除失败'))
      
      render(
        <TestWrapper>
          <ChannelManagement />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByTestId('channel-list')).toBeInTheDocument()
      })

      const deleteButton = screen.getAllByText('删除')[0]
      await user.click(deleteButton)

      const confirmButton = screen.getByText('确定删除')
      await user.click(confirmButton)

      await waitFor(() => {
        expect(screen.getByText('删除失败，请重试')).toBeInTheDocument()
      })
    })
  })
})
